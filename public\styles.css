/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.nav-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 主要内容区域 */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 输入页面样式 */
.input-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.input-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 2rem;
}

.input-methods {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.method-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.method-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.method-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.input-method {
    display: none;
}

.input-method.active {
    display: block;
}

#text-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    resize: vertical;
    margin-bottom: 1rem;
    transition: border-color 0.3s ease;
}

#text-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.secondary-btn {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
}

/* 搜索样式 */
.search-box {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

#search-query {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

#search-query:focus {
    outline: none;
    border-color: #667eea;
}

.search-results {
    display: grid;
    gap: 1rem;
}

.search-result-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #e3f2fd;
    border-color: #667eea;
    transform: translateY(-2px);
}

/* 文件上传样式 */
.upload-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e3f2fd;
}

.upload-area i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.upload-area p {
    color: #666;
    margin-bottom: 1rem;
}

/* 学习模式样式 */
.learning-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    height: calc(100vh - 120px);
}

.original-text-panel,
.translation-panel,
.word-details-panel {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.translation-panel {
    grid-column: 1;
    margin-top: 1rem;
}

.word-details-panel {
    grid-column: 2;
    grid-row: 1 / 3;
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.text-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.text-content {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    line-height: 1.8;
    font-size: 1.1rem;
}

.sentence {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    cursor: pointer;
    position: relative;
}

.sentence:hover {
    background: #f0f8ff;
}

.sentence.playing {
    background: #e3f2fd;
    border-left: 4px solid #667eea;
}

.word {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.3s ease;
    position: relative;
}

.word:hover {
    background: #fff3cd;
    color: #856404;
}

.word.selected {
    background: #667eea;
    color: white;
}

.sentence-controls {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sentence:hover .sentence-controls {
    opacity: 1;
}

.play-sentence-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* 单词详情面板 */
.word-details-content {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.word-placeholder {
    text-align: center;
    color: #999;
    padding: 2rem;
}

.word-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ddd;
}

.word-info {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

.word-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.word-phonetic {
    color: #666;
    font-style: italic;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.play-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.3rem 0.6rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.word-meanings {
    margin-bottom: 1.5rem;
}

.word-meanings h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.meaning-item {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border-left: 3px solid #667eea;
}

.word-examples,
.word-phrases {
    margin-bottom: 1.5rem;
}

.word-examples h4,
.word-phrases h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.example-item,
.phrase-item {
    background: #fff;
    border: 1px solid #e0e0e0;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.example-en {
    font-style: italic;
    color: #333;
    margin-bottom: 0.3rem;
}

.example-zh {
    color: #666;
    font-size: 0.9rem;
}

/* 进度页面样式 */
.progress-layout {
    display: grid;
    gap: 2rem;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.3rem;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

.progress-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.chart-card,
.word-list-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.chart-card h3,
.word-list-card h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.3rem;
}

.progress-bar {
    background: #e0e0e0;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.recent-words {
    display: grid;
    gap: 0.75rem;
}

.word-item {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    align-items: center;
}

.word-item .word {
    font-weight: 600;
    color: #333;
    cursor: default;
}

.word-item .meaning {
    color: #666;
    cursor: default;
}

.status {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

.status.mastered {
    background: #d4edda;
    color: #155724;
}

.status.learning {
    background: #fff3cd;
    color: #856404;
}

.status.new {
    background: #cce5ff;
    color: #004085;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.word-pronunciation {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

#modal-phonetic {
    font-style: italic;
    color: #666;
    font-size: 1.1rem;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        width: 100%;
        justify-content: center;
    }
    
    .learning-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
        height: auto;
    }
    
    .word-details-panel {
        grid-column: 1;
        grid-row: auto;
        order: 3;
    }
    
    .progress-charts {
        grid-template-columns: 1fr;
    }
    
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .input-methods {
        flex-direction: column;
        align-items: center;
    }
    
    .search-box {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .word-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 选中文本样式 */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: #333;
}

/* 焦点样式 */
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 成功/错误消息 */
.message {
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    font-weight: 500;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.info {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

/* 打印样式 */
@media print {
    .header,
    .nav,
    .control-btn,
    .word-details-panel {
        display: none !important;
    }
    
    .learning-layout {
        grid-template-columns: 1fr;
    }
    
    body {
        background: white;
    }
    
    .original-text-panel,
    .translation-panel {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}