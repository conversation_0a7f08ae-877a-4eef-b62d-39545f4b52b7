@echo off
chcp 65001 >nul
title 英语背单词系统启动器
color 0A

echo.
echo ===============================================
echo           英语背单词系统启动器 v1.0
echo ===============================================
echo.
echo 🚀 正在启动服务器...
echo 📱 启动完成后将自动打开浏览器
echo ⚠️  按 Ctrl+C 可停止服务器
echo.
echo 📋 系统检查中...

REM 切换到项目目录
cd /d "%~dp0"

REM 检查环境变量文件
if not exist ".env" (
    echo ❌ 错误：找不到 .env 配置文件
    echo 💡 提示：请确保 .env 文件存在于项目根目录
    echo.
    pause
    exit /b 1
)
echo ✅ 配置文件检查通过

REM 检查 Node.js 是否安装
echo 🔍 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到 Node.js
    echo 💡 请先安装 Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 检查 npm 是否可用
echo 🔍 检查 npm 环境...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：npm 不可用
    echo 💡 请重新安装 Node.js
    echo.
    pause
    exit /b 1
)
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm 版本: %NPM_VERSION%

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖包...
    echo 💭 这可能需要几分钟时间，请耐心等待...
    npm install
    if errorlevel 1 (
        echo ❌ 错误：依赖安装失败
        echo 💡 请检查网络连接或尝试使用 npm install --registry https://registry.npmmirror.com
        echo.
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包已存在
)

echo.
echo ===============================================
echo 🚀 启动服务器
echo ===============================================

REM 检查端口是否被占用
echo 🔍 检查端口 3001 是否可用...
netstat -ano | findstr :3001 >nul
if not errorlevel 1 (
    echo ⚠️  警告：端口 3001 已被占用
    echo 💡 请关闭占用端口的程序或修改 .env 文件中的端口配置
    echo.
    set /p choice="是否继续启动？(y/n): "
    if /i not "%choice%"=="y" (
        echo 启动已取消
        pause
        exit /b 1
    )
)

echo ✅ 端口检查完成
echo.
echo 🌐 服务器启动中...
echo 📱 浏览器将在 3 秒后自动打开
echo 🔗 访问地址: http://localhost:3001
echo.
echo ⚠️  重要提示：
echo    - 请保持此窗口打开
echo    - 按 Ctrl+C 可停止服务器
echo    - 如果浏览器未自动打开，请手动访问上述地址
echo.

REM 延迟启动浏览器
timeout /t 3 /nobreak >nul
start "" "http://localhost:3001"

REM 启动服务器
npm start

echo.
echo ===============================================
echo 🛑 服务器已停止
echo ===============================================
echo 感谢使用英语背单词系统！
pause