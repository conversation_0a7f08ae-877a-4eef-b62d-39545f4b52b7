/**
 * 文本处理服务
 * 提供文本分析、单词提取、句子分割等功能
 */

class TextProcessingService {
  constructor() {
    // 常见的停用词
    this.stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'shall',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
      'my', 'your', 'his', 'her', 'its', 'our', 'their', 'this', 'that', 'these', 'those',
      'not', 'no', 'yes', 'so', 'very', 'just', 'now', 'then', 'here', 'there', 'where', 'when',
      'what', 'who', 'why', 'how', 'all', 'any', 'some', 'each', 'every', 'other', 'another',
      'more', 'most', 'much', 'many', 'few', 'little', 'less', 'least', 'only', 'also', 'too',
      'as', 'if', 'than', 'because', 'since', 'while', 'during', 'before', 'after', 'until',
      'from', 'into', 'onto', 'upon', 'about', 'above', 'below', 'under', 'over', 'through',
      'between', 'among', 'within', 'without', 'against', 'across', 'around', 'behind',
      'beside', 'beyond', 'inside', 'outside', 'toward', 'towards', 'up', 'down', 'out', 'off'
    ]);
    
    // 难度级别词汇表（简化版）
    this.difficultyLevels = {
      beginner: new Set([
        'hello', 'world', 'good', 'bad', 'big', 'small', 'new', 'old', 'first', 'last',
        'long', 'short', 'high', 'low', 'right', 'left', 'next', 'same', 'different',
        'important', 'possible', 'early', 'late', 'young', 'great', 'little', 'own',
        'other', 'old', 'right', 'big', 'high', 'different', 'small', 'large', 'next',
        'early', 'young', 'important', 'few', 'public', 'bad', 'same', 'able'
      ]),
      intermediate: new Set([
        'government', 'system', 'program', 'question', 'work', 'group', 'case', 'part',
        'place', 'number', 'point', 'week', 'company', 'where', 'problem', 'fact',
        'hand', 'right', 'thing', 'life', 'eye', 'way', 'head', 'day', 'time',
        'person', 'year', 'water', 'history', 'today', 'book', 'method', 'name',
        'area', 'money', 'story', 'month', 'lot', 'study', 'book', 'eye', 'job',
        'word', 'business', 'issue', 'side', 'kind', 'four', 'head', 'far', 'black',
        'long', 'both', 'little', 'house', 'yes', 'after', 'since', 'long', 'provide',
        'service', 'around', 'friend', 'important', 'father', 'sit', 'away', 'until'
      ])
    };
  }

  /**
   * 分析文本内容
   * @param {string} text - 要分析的文本
   * @returns {Object} 分析结果
   */
  analyzeText(text) {
    if (!text || typeof text !== 'string') {
      return {
        wordCount: 0,
        sentenceCount: 0,
        paragraphCount: 0,
        averageWordsPerSentence: 0,
        readingTime: 0,
        difficulty: 'unknown',
        languageDetection: 'unknown'
      };
    }

    const words = this.extractWords(text);
    const sentences = this.extractSentences(text);
    const paragraphs = this.extractParagraphs(text);
    
    const wordCount = words.length;
    const sentenceCount = sentences.length;
    const paragraphCount = paragraphs.length;
    const averageWordsPerSentence = sentenceCount > 0 ? Math.round(wordCount / sentenceCount * 100) / 100 : 0;
    
    // 估算阅读时间（假设每分钟200词）
    const readingTime = Math.ceil(wordCount / 200);
    
    // 检测语言
    const languageDetection = this.detectLanguage(text);
    
    // 评估难度
    const difficulty = this.assessDifficulty(words, sentences);
    
    return {
      wordCount,
      sentenceCount,
      paragraphCount,
      averageWordsPerSentence,
      readingTime,
      difficulty,
      languageDetection,
      characterCount: text.length,
      characterCountNoSpaces: text.replace(/\s/g, '').length
    };
  }

  /**
   * 提取单词
   * @param {string} text - 文本内容
   * @returns {Array} 单词数组
   */
  extractWords(text) {
    if (!text) return [];
    
    // 移除HTML标签
    const cleanText = text.replace(/<[^>]*>/g, ' ');
    
    // 提取单词（支持英文和中文）
    const words = [];
    
    // 英文单词匹配
    const englishWords = cleanText.match(/\b[a-zA-Z]+\b/g) || [];
    words.push(...englishWords);
    
    // 中文字符匹配
    const chineseChars = cleanText.match(/[\u4e00-\u9fff]/g) || [];
    words.push(...chineseChars);
    
    return words.map(word => ({
      text: word.toLowerCase(),
      original: word,
      position: text.indexOf(word),
      length: word.length,
      isStopWord: this.isStopWord(word),
      difficulty: this.getWordDifficulty(word)
    })).filter(word => word.text.length > 0);
  }

  /**
   * 提取句子
   * @param {string} text - 文本内容
   * @returns {Array} 句子数组
   */
  extractSentences(text) {
    if (!text) return [];
    
    // 移除HTML标签
    const cleanText = text.replace(/<[^>]*>/g, ' ');
    
    // 按句号、问号、感叹号分割句子
    const sentences = cleanText
      .split(/[.!?。！？]+/)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
      .map((sentence, index) => {
        const words = this.extractWords(sentence);
        return {
          text: sentence,
          index: index + 1,
          wordCount: words.length,
          characterCount: sentence.length,
          difficulty: this.assessSentenceDifficulty(words),
          hasComplexStructure: this.hasComplexStructure(sentence)
        };
      });
    
    return sentences;
  }

  /**
   * 提取段落
   * @param {string} text - 文本内容
   * @returns {Array} 段落数组
   */
  extractParagraphs(text) {
    if (!text) return [];
    
    // 移除HTML标签
    const cleanText = text.replace(/<[^>]*>/g, ' ');
    
    // 按双换行符分割段落
    const paragraphs = cleanText
      .split(/\n\s*\n/)
      .map(paragraph => paragraph.trim())
      .filter(paragraph => paragraph.length > 0)
      .map((paragraph, index) => {
        const sentences = this.extractSentences(paragraph);
        const words = this.extractWords(paragraph);
        
        return {
          text: paragraph,
          index: index + 1,
          sentenceCount: sentences.length,
          wordCount: words.length,
          characterCount: paragraph.length
        };
      });
    
    return paragraphs;
  }

  /**
   * 检测语言
   * @param {string} text - 文本内容
   * @returns {string} 语言类型
   */
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
    const totalChars = chineseChars + englishChars;
    
    if (totalChars === 0) return 'unknown';
    
    const chineseRatio = chineseChars / totalChars;
    const englishRatio = englishChars / totalChars;
    
    if (chineseRatio > 0.5) return 'chinese';
    if (englishRatio > 0.5) return 'english';
    if (chineseRatio > 0.2 && englishRatio > 0.2) return 'mixed';
    
    return 'unknown';
  }

  /**
   * 评估文本难度
   * @param {Array} words - 单词数组
   * @param {Array} sentences - 句子数组
   * @returns {string} 难度级别
   */
  assessDifficulty(words, sentences) {
    if (!words.length || !sentences.length) return 'unknown';
    
    let difficultyScore = 0;
    
    // 基于单词难度评分
    const wordDifficultyScores = words.map(word => {
      switch (word.difficulty) {
        case 'beginner': return 1;
        case 'intermediate': return 2;
        case 'advanced': return 3;
        default: return 2;
      }
    });
    
    const averageWordDifficulty = wordDifficultyScores.reduce((sum, score) => sum + score, 0) / wordDifficultyScores.length;
    
    // 基于句子长度评分
    const averageSentenceLength = sentences.reduce((sum, sentence) => sum + sentence.wordCount, 0) / sentences.length;
    
    // 基于复杂句子结构评分
    const complexSentenceRatio = sentences.filter(s => s.hasComplexStructure).length / sentences.length;
    
    // 综合评分
    difficultyScore = averageWordDifficulty * 0.4 + 
                     (averageSentenceLength / 20) * 0.3 + 
                     complexSentenceRatio * 2 * 0.3;
    
    if (difficultyScore < 1.5) return 'beginner';
    if (difficultyScore < 2.5) return 'intermediate';
    return 'advanced';
  }

  /**
   * 判断是否为停用词
   * @param {string} word - 单词
   * @returns {boolean} 是否为停用词
   */
  isStopWord(word) {
    return this.stopWords.has(word.toLowerCase());
  }

  /**
   * 获取单词难度
   * @param {string} word - 单词
   * @returns {string} 难度级别
   */
  getWordDifficulty(word) {
    const lowerWord = word.toLowerCase();
    
    if (this.difficultyLevels.beginner.has(lowerWord)) {
      return 'beginner';
    }
    
    if (this.difficultyLevels.intermediate.has(lowerWord)) {
      return 'intermediate';
    }
    
    // 基于单词长度的简单判断
    if (word.length <= 4) return 'beginner';
    if (word.length <= 8) return 'intermediate';
    return 'advanced';
  }

  /**
   * 评估句子难度
   * @param {Array} words - 句子中的单词
   * @returns {string} 难度级别
   */
  assessSentenceDifficulty(words) {
    if (!words.length) return 'unknown';
    
    const difficultyScores = words.map(word => {
      switch (word.difficulty) {
        case 'beginner': return 1;
        case 'intermediate': return 2;
        case 'advanced': return 3;
        default: return 2;
      }
    });
    
    const averageScore = difficultyScores.reduce((sum, score) => sum + score, 0) / difficultyScores.length;
    
    if (averageScore < 1.5) return 'beginner';
    if (averageScore < 2.5) return 'intermediate';
    return 'advanced';
  }

  /**
   * 检查是否有复杂句子结构
   * @param {string} sentence - 句子
   * @returns {boolean} 是否有复杂结构
   */
  hasComplexStructure(sentence) {
    // 简单的复杂结构检测
    const complexPatterns = [
      /,.*,/,  // 多个逗号
      /;/,     // 分号
      /:/,     // 冒号
      /\([^)]*\)/,  // 括号
      /"[^"]*"/,   // 引号
      /which|that|who|whom|whose|where|when/i,  // 关系代词
      /although|though|however|nevertheless|furthermore|moreover/i,  // 连接词
      /if.*then|either.*or|neither.*nor/i  // 条件句
    ];
    
    return complexPatterns.some(pattern => pattern.test(sentence));
  }

  /**
   * 获取关键词
   * @param {string} text - 文本内容
   * @param {number} limit - 返回关键词数量限制
   * @returns {Array} 关键词数组
   */
  getKeywords(text, limit = 10) {
    const words = this.extractWords(text);
    
    // 过滤停用词和短词
    const meaningfulWords = words.filter(word => 
      !word.isStopWord && 
      word.text.length > 2 &&
      /^[a-zA-Z\u4e00-\u9fff]+$/.test(word.text)
    );
    
    // 统计词频
    const wordFreq = {};
    meaningfulWords.forEach(word => {
      wordFreq[word.text] = (wordFreq[word.text] || 0) + 1;
    });
    
    // 按频率排序并返回前N个
    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([word, frequency]) => ({
        word,
        frequency,
        difficulty: this.getWordDifficulty(word)
      }));
  }

  /**
   * 生成文本摘要
   * @param {string} text - 文本内容
   * @param {number} maxSentences - 最大句子数
   * @returns {string} 摘要
   */
  generateSummary(text, maxSentences = 3) {
    const sentences = this.extractSentences(text);
    
    if (sentences.length <= maxSentences) {
      return sentences.map(s => s.text).join('. ') + '.';
    }
    
    // 简单的摘要算法：选择最长的句子
    const sortedSentences = sentences
      .sort((a, b) => b.wordCount - a.wordCount)
      .slice(0, maxSentences)
      .sort((a, b) => a.index - b.index);  // 按原始顺序排列
    
    return sortedSentences.map(s => s.text).join('. ') + '.';
  }

  /**
   * 清理文本
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanText(text) {
    if (!text) return '';
    
    return text
      .replace(/<[^>]*>/g, ' ')  // 移除HTML标签
      .replace(/\s+/g, ' ')      // 合并多个空格
      .replace(/[\r\n]+/g, '\n') // 规范化换行符
      .trim();                   // 移除首尾空格
  }
}

module.exports = TextProcessingService;