/**
 * 用户认证中间件
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { AppError, asyncHandler } = require('./errorHandler');
const logger = require('../utils/logger');
const DatabaseService = require('../services/databaseService');

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
    this.bcryptRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    this.db = new DatabaseService();
  }

  /**
   * 生成JWT令牌
   */
  generateToken(userId) {
    return jwt.sign({ userId }, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn
    });
  }

  /**
   * 验证JWT令牌
   */
  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      throw new AppError('Invalid token', 401, 'INVALID_TOKEN');
    }
  }

  /**
   * 哈希密码
   */
  async hashPassword(password) {
    return await bcrypt.hash(password, this.bcryptRounds);
  }

  /**
   * 验证密码
   */
  async verifyPassword(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  }

  /**
   * 用户注册
   */
  async register(username, email, password) {
    try {
      // 检查用户是否已存在
      const existingUser = await this.db.getUserByEmail(email);
      if (existingUser) {
        throw new AppError('User already exists', 400, 'USER_EXISTS');
      }

      // 哈希密码
      const hashedPassword = await this.hashPassword(password);

      // 创建用户
      const userId = await this.db.createUser({
        username,
        email,
        password_hash: hashedPassword
      });

      // 生成令牌
      const token = this.generateToken(userId);

      logger.info(`User registered: ${email}`);

      return {
        user: {
          id: userId,
          username,
          email
        },
        token
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * 用户登录
   */
  async login(email, password) {
    try {
      // 获取用户
      const user = await this.db.getUserByEmail(email);
      if (!user) {
        throw new AppError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
      }

      // 验证密码
      const isValidPassword = await this.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        throw new AppError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
      }

      // 更新最后登录时间
      await this.db.updateUserLastLogin(user.id);

      // 生成令牌
      const token = this.generateToken(user.id);

      logger.info(`User logged in: ${email}`);

      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email
        },
        token
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(userId) {
    try {
      const user = await this.db.getUserById(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        created_at: user.created_at,
        last_login: user.last_login
      };
    } catch (error) {
      logger.error('Get current user failed:', error);
      throw error;
    }
  }
}

// 创建认证服务实例
const authService = new AuthService();

/**
 * 认证中间件
 */
const authenticate = asyncHandler(async (req, res, next) => {
  // 获取令牌
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    throw new AppError('No token provided', 401, 'NO_TOKEN');
  }

  // 验证令牌
  const decoded = authService.verifyToken(token);
  
  // 获取用户信息
  const user = await authService.getCurrentUser(decoded.userId);
  
  // 将用户信息添加到请求对象
  req.user = user;
  
  next();
});

/**
 * 可选认证中间件（不强制要求登录）
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  try {
    let token;
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      const decoded = authService.verifyToken(token);
      const user = await authService.getCurrentUser(decoded.userId);
      req.user = user;
    }
  } catch (error) {
    // 忽略认证错误，继续处理请求
    logger.debug('Optional auth failed:', error.message);
  }
  
  next();
});

/**
 * 权限检查中间件
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    if (roles.length && !roles.includes(req.user.role)) {
      throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    next();
  };
};

module.exports = {
  authService,
  authenticate,
  optionalAuth,
  authorize
};
