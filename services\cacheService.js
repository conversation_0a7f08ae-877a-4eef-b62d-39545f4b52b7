/**
 * 缓存服务
 * 提供内存缓存和Redis缓存功能
 */

const redis = require('redis');
const logger = require('../utils/logger');

class CacheService {
  constructor() {
    this.memoryCache = new Map();
    this.redisClient = null;
    this.defaultTTL = parseInt(process.env.CACHE_TTL || '3600'); // 1小时
    this.maxSize = parseInt(process.env.CACHE_MAX_SIZE || '1000');
    
    this.initializeRedis();
  }

  /**
   * 初始化Redis连接
   */
  async initializeRedis() {
    try {
      if (process.env.REDIS_HOST) {
        this.redisClient = redis.createClient({
          host: process.env.REDIS_HOST,
          port: process.env.REDIS_PORT || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
          retry_strategy: (options) => {
            if (options.error && options.error.code === 'ECONNREFUSED') {
              logger.error('Redis server connection refused');
              return new Error('Redis server connection refused');
            }
            if (options.total_retry_time > 1000 * 60 * 60) {
              return new Error('Retry time exhausted');
            }
            if (options.attempt > 10) {
              return undefined;
            }
            return Math.min(options.attempt * 100, 3000);
          }
        });

        this.redisClient.on('connect', () => {
          logger.info('Redis client connected');
        });

        this.redisClient.on('error', (err) => {
          logger.error('Redis client error:', err);
          this.redisClient = null;
        });

        await this.redisClient.connect();
      } else {
        logger.info('Redis not configured, using memory cache only');
      }
    } catch (error) {
      logger.error('Redis initialization failed:', error);
      this.redisClient = null;
    }
  }

  /**
   * 生成缓存键
   */
  generateKey(prefix, ...parts) {
    return `${prefix}:${parts.join(':')}`;
  }

  /**
   * 检查内存缓存大小并清理
   */
  checkMemoryCacheSize() {
    if (this.memoryCache.size >= this.maxSize) {
      // 删除最老的条目
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }
  }

  /**
   * 设置缓存
   */
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serializedValue = JSON.stringify({
        data: value,
        timestamp: Date.now(),
        ttl: ttl * 1000
      });

      // Redis缓存
      if (this.redisClient) {
        await this.redisClient.setEx(key, ttl, serializedValue);
      }

      // 内存缓存
      this.checkMemoryCacheSize();
      this.memoryCache.set(key, serializedValue);

      logger.debug(`Cache set: ${key}`);
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * 获取缓存
   */
  async get(key) {
    try {
      let serializedValue = null;

      // 先尝试Redis
      if (this.redisClient) {
        try {
          serializedValue = await this.redisClient.get(key);
        } catch (error) {
          logger.warn('Redis get error, falling back to memory cache:', error);
        }
      }

      // 如果Redis没有，尝试内存缓存
      if (!serializedValue && this.memoryCache.has(key)) {
        serializedValue = this.memoryCache.get(key);
      }

      if (!serializedValue) {
        return null;
      }

      const cached = JSON.parse(serializedValue);
      
      // 检查是否过期
      if (Date.now() - cached.timestamp > cached.ttl) {
        await this.delete(key);
        return null;
      }

      logger.debug(`Cache hit: ${key}`);
      return cached.data;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async delete(key) {
    try {
      // Redis删除
      if (this.redisClient) {
        await this.redisClient.del(key);
      }

      // 内存缓存删除
      this.memoryCache.delete(key);

      logger.debug(`Cache deleted: ${key}`);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * 清空所有缓存
   */
  async clear() {
    try {
      // 清空Redis
      if (this.redisClient) {
        await this.redisClient.flushAll();
      }

      // 清空内存缓存
      this.memoryCache.clear();

      logger.info('All cache cleared');
      return true;
    } catch (error) {
      logger.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats() {
    const stats = {
      memoryCache: {
        size: this.memoryCache.size,
        maxSize: this.maxSize
      },
      redis: {
        connected: !!this.redisClient
      }
    };

    if (this.redisClient) {
      try {
        const info = await this.redisClient.info('memory');
        stats.redis.memory = info;
      } catch (error) {
        logger.warn('Failed to get Redis stats:', error);
      }
    }

    return stats;
  }

  /**
   * 缓存装饰器
   */
  cached(prefix, ttl = this.defaultTTL) {
    return (target, propertyName, descriptor) => {
      const method = descriptor.value;
      
      descriptor.value = async function(...args) {
        const key = this.generateKey(prefix, ...args);
        
        // 尝试从缓存获取
        let result = await this.get(key);
        if (result !== null) {
          return result;
        }
        
        // 执行原方法
        result = await method.apply(this, args);
        
        // 存入缓存
        await this.set(key, result, ttl);
        
        return result;
      }.bind(this);
      
      return descriptor;
    };
  }

  /**
   * 关闭连接
   */
  async close() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
    this.memoryCache.clear();
    logger.info('Cache service closed');
  }
}

// 创建单例实例
const cacheService = new CacheService();

module.exports = cacheService;
