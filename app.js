const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
require('dotenv').config();

// 导入中间件
const { globalErrorHandler, notFoundHandler, handleUncaughtException, handleUnhandledRejection } = require('./middleware/errorHandler');
const { apiLimiter, speedLimiter } = require('./middleware/rateLimiter');
const logger = require('./utils/logger');

// 导入路由
const aiRoutes = require('./routes/aiRoutes');
const authRoutes = require('./routes/authRoutes');

const app = express();
const PORT = process.env.PORT || 3000;

// 处理未捕获的异常
handleUncaughtException();
handleUnhandledRejection();

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS配置
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// 基础中间件
app.use(express.json({ limit: process.env.MAX_FILE_SIZE || '10mb' }));
app.use(express.urlencoded({ extended: true, limit: process.env.MAX_FILE_SIZE || '10mb' }));

// 限流中间件
app.use(speedLimiter);
app.use('/api/', apiLimiter);

// HTTP请求日志中间件
app.use((req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - start;
    logger.httpLog(req, res, responseTime);
  });

  next();
});

// 静态文件服务 - 为前端文件提供服务
app.use(express.static(path.join(__dirname, 'public')));

// API 路由
app.use('/api/ai', aiRoutes);
app.use('/api/auth', authRoutes);

// 健康检查端点
app.get('/health', async (req, res) => {
  const healthCheck = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development'
  };

  // 检查数据库连接
  try {
    const DatabaseService = require('./services/databaseService');
    const db = new DatabaseService();
    await db.testConnection();
    healthCheck.database = 'connected';
  } catch (error) {
    healthCheck.database = 'disconnected';
    healthCheck.status = 'WARNING';
  }

  // 检查缓存服务
  try {
    const cacheService = require('./services/cacheService');
    const stats = await cacheService.getStats();
    healthCheck.cache = stats;
  } catch (error) {
    healthCheck.cache = 'unavailable';
  }

  const statusCode = healthCheck.status === 'OK' ? 200 : 503;
  res.status(statusCode).json(healthCheck);
});

// 根路径重定向到前端页面
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404 处理
app.use('*', notFoundHandler);

// 全局错误处理
app.use(globalErrorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 English Learning Backend Server running on port ${PORT}`);
  console.log(`📱 Frontend available at: http://localhost:${PORT}`);
  console.log(`🔗 API base URL: http://localhost:${PORT}/api`);
  console.log(`💚 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;