@echo off
echo 测试启动脚本
echo 当前目录: %CD%
echo.

echo 检查 Node.js...
node --version
if errorlevel 1 (
    echo Node.js 未安装
    pause
    exit /b 1
)

echo.
echo 检查文件...
if exist "app.js" (
    echo ✅ app.js 存在
) else (
    echo ❌ app.js 不存在
    pause
    exit /b 1
)

if exist ".env" (
    echo ✅ .env 存在
) else (
    echo ❌ .env 不存在
    pause
    exit /b 1
)

if exist "package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
    pause
    exit /b 1
)

echo.
echo 启动服务器...
node app.js
