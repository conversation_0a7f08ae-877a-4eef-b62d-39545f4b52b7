' 创建桌面快捷方式的VBS脚本
Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\英语背单词系统.lnk")

' 获取当前脚本所在目录
strCurrentDir = Left(WScript.ScriptFullName, InStrRev(WScript.ScriptFullName, "\"))

' 设置快捷方式属性
oShellLink.TargetPath = strCurrentDir & "启动系统.bat"
oShellLink.WorkingDirectory = strCurrentDir
oShellLink.Description = "英语背单词系统 - 一键启动"
oShellLink.IconLocation = "shell32.dll,21"  ' 使用系统图标

' 保存快捷方式
oShellLink.Save

' 显示完成消息
MsgBox "桌面快捷方式创建成功！" & vbCrLf & vbCrLf & "快捷方式名称：英语背单词系统" & vbCrLf & "位置：桌面", vbInformation, "创建完成"

Set oShellLink = Nothing
Set WshShell = Nothing