// 应用状态管理
class AppState {
    constructor() {
        this.state = {
            // 学习内容
            currentText: '',
            currentTranslation: '',
            sentences: [],
            words: [],
            selectedWord: null,

            // 播放状态
            isPlaying: false,
            currentSentenceIndex: -1,

            // 用户认证
            user: null,
            token: localStorage.getItem('authToken'),

            // 学习进度
            learningStats: {
                totalWords: 0,
                masteredWords: 0,
                learningWords: 0,
                newWords: 0,
                studyTime: 0,
                streak: 0
            },

            // 最近学习的单词
            recentWords: []
        };

        this.listeners = new Map();
        this.initializeAuth();
    }

    // 获取状态
    get(key) {
        return key ? this.state[key] : this.state;
    }

    // 设置状态
    set(key, value) {
        const oldValue = this.state[key];
        this.state[key] = value;
        this.notify(key, value, oldValue);
    }

    // 更新状态（合并对象）
    update(updates) {
        Object.keys(updates).forEach(key => {
            this.set(key, updates[key]);
        });
    }

    // 监听状态变化
    subscribe(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, []);
        }
        this.listeners.get(key).push(callback);

        // 返回取消订阅函数
        return () => {
            const callbacks = this.listeners.get(key);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        };
    }

    // 通知监听器
    notify(key, newValue, oldValue) {
        const callbacks = this.listeners.get(key) || [];
        callbacks.forEach(callback => {
            try {
                callback(newValue, oldValue);
            } catch (error) {
                console.error('State listener error:', error);
            }
        });
    }

    // 初始化认证状态
    async initializeAuth() {
        if (this.state.token) {
            try {
                const user = await this.fetchCurrentUser();
                this.set('user', user);
                await this.loadUserProgress();
            } catch (error) {
                console.warn('Failed to load user:', error);
                this.clearAuth();
            }
        }
    }

    // 设置认证信息
    setAuth(user, token) {
        this.set('user', user);
        this.set('token', token);
        localStorage.setItem('authToken', token);
    }

    // 清除认证信息
    clearAuth() {
        this.set('user', null);
        this.set('token', null);
        localStorage.removeItem('authToken');
    }

    // 获取当前用户
    async fetchCurrentUser() {
        const response = await fetch('/api/auth/me', {
            headers: {
                'Authorization': `Bearer ${this.state.token}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch user');
        }

        return await response.json();
    }

    // 加载用户学习进度
    async loadUserProgress() {
        try {
            const response = await fetch('/api/progress', {
                headers: {
                    'Authorization': `Bearer ${this.state.token}`
                }
            });

            if (response.ok) {
                const progress = await response.json();
                this.set('learningStats', progress.stats);
                this.set('recentWords', progress.recentWords);
            }
        } catch (error) {
            console.warn('Failed to load progress:', error);
        }
    }
}

// 创建全局状态实例
const appState = new AppState();

// 兼容性：保持原有的全局变量（逐步迁移）
let currentText = '';
let currentTranslation = '';
let sentences = [];
let words = [];
let selectedWord = null;
let isPlaying = false;
let currentSentenceIndex = -1;

// 已移除模拟数据，现在完全依赖API调用

// 学习进度数据
let learningStats = {
    totalWords: 156,
    masteredWords: 89,
    learningWords: 45,
    newWords: 22,
    studyTime: 127, // 分钟
    streak: 7 // 连续学习天数
};

let recentWords = [
    { word: "innovation", meaning: "创新", status: "mastered" },
    { word: "sustainable", meaning: "可持续的", status: "learning" },
    { word: "biodiversity", meaning: "生物多样性", status: "new" },
    { word: "artificial", meaning: "人工的", status: "learning" },
    { word: "revolution", meaning: "革命", status: "mastered" }
];

// DOM 加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupEventListeners();
    updateProgressStats();
    renderRecentWords();
    showTab('input');
}

function setupEventListeners() {
    // 导航按钮
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tab = e.target.dataset.tab;
            showTab(tab);
        });
    });

    // 输入方法切换
    document.querySelectorAll('.method-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const method = e.target.dataset.method;
            switchInputMethod(method);
        });
    });

    // 文本输入处理
    document.getElementById('analyze-btn').addEventListener('click', processManualText);
    document.getElementById('search-btn').addEventListener('click', performSearch);
    document.getElementById('search-query').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 文件上传
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // 学习模式控制
    document.getElementById('play-all-btn').addEventListener('click', playAllSentences);
    document.getElementById('translate-all-btn').addEventListener('click', toggleTranslation);
    document.getElementById('font-size-btn').addEventListener('click', adjustFontSize);

    // 模态框关闭
    document.getElementById('close-modal').addEventListener('click', closeWordModal);
    document.getElementById('word-modal').addEventListener('click', (e) => {
        if (e.target.id === 'word-modal') {
            closeWordModal();
        }
    });
    
    // 单词详情面板关闭
    document.getElementById('close-word-details').addEventListener('click', closeWordDetails);
}

// 标签页切换
function showTab(tabName) {
    // 更新导航按钮状态
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });

    // 显示对应内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName + '-tab').classList.add('active');
}

// 输入方法切换
function switchInputMethod(method) {
    // 更新按钮状态
    document.querySelectorAll('.method-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.method === method) {
            btn.classList.add('active');
        }
    });

    // 显示对应输入方式
    document.querySelectorAll('.input-method').forEach(methodDiv => {
        methodDiv.classList.remove('active');
    });
    document.getElementById(method + '-input').classList.add('active');
}

// 处理手动输入的文本
function processManualText() {
    const text = document.getElementById('text-input').value.trim();
    if (!text) {
        showMessage('请输入要学习的英文文本', 'error');
        return;
    }
    
    processText(text);
}

// 执行AI搜索
async function performSearch() {
    const query = document.getElementById('search-query').value.trim();
    if (!query) {
        showMessage('请输入搜索关键词', 'error');
        return;
    }

    showLoading('正在搜索相关文章...');
    
    try {
        const response = await fetch('/api/ai/search-articles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                query: query,
                difficulty: 'intermediate'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 将API返回的文章转换为搜索结果格式
            const results = [{
                title: data.data.title || `关于"${query}"的文章`,
                content: data.data.content,
                source: 'AI生成'
            }];
            displaySearchResults(results);
        } else {
            showMessage('搜索失败: ' + (data.error || '未知错误'), 'error');
            displaySearchResults([]);
        }
    } catch (error) {
        console.error('搜索错误:', error);
        showMessage('网络错误，请检查连接后重试', 'error');
        displaySearchResults([]);
    } finally {
        hideLoading();
    }
}

// 显示搜索结果
function displaySearchResults(results) {
    const container = document.getElementById('search-results');
    container.innerHTML = '';

    if (results.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>暂无搜索结果</p>
                <p>请尝试其他关键词或检查网络连接</p>
            </div>
        `;
        return;
    }

    results.forEach((result, index) => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'search-result-item';
        resultDiv.innerHTML = `
            <h4>${result.title}</h4>
            <p>${result.content}</p>
            <small>来源: ${result.source}</small>
        `;
        resultDiv.addEventListener('click', () => {
            processText(result.content);
            showMessage('文章已加载到学习模式', 'success');
        });
        container.appendChild(resultDiv);
    });
}

// 文件拖拽处理
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

// 处理上传的文件
function processFile(file) {
    if (!file.type.startsWith('text/')) {
        showMessage('请上传文本文件', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const text = e.target.result;
        processText(text);
        showMessage('文件已成功加载', 'success');
    };
    reader.readAsText(file);
}

// 处理文本内容
async function processText(text) {
    showLoading('正在处理文本...');
    
    try {
        currentText = text;
        sentences = splitIntoSentences(text);
        words = extractWords(text);
        
        // 翻译文本
        currentTranslation = await translateText(text);
        
        displayLearningContent();
        hideLoading();
        showTab('learning');
        showMessage('文本处理完成，开始学习吧！', 'success');
    } catch (error) {
         console.error('文本处理错误:', error);
         hideLoading();
         showMessage('文本处理失败，请重试', 'error');
     }
 }

// 分割句子
function splitIntoSentences(text) {
    return text.match(/[^.!?]+[.!?]+/g) || [text];
}

// 提取单词
function extractWords(text) {
    return text.toLowerCase().match(/\b[a-z]+\b/g) || [];
}

// 翻译文本
async function translateText(text) {
    try {
        const response = await fetch('/api/ai/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                text: text,
                from: 'en',
                to: 'zh'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            return data.data.translation;
        } else {
            console.error('翻译失败:', data.error);
            // 降级到模拟翻译
            return `[翻译] ${text}`;
        }
    } catch (error) {
        console.error('翻译API调用错误:', error);
        // 降级到模拟翻译
        return `[翻译] ${text}`;
    }
}

// 显示学习内容
function displayLearningContent() {
    displayOriginalText();
    displayTranslation();
    clearWordDetails();
}

// 显示原文
function displayOriginalText() {
    const container = document.getElementById('original-text');
    container.innerHTML = '';

    sentences.forEach((sentence, index) => {
        const sentenceDiv = document.createElement('div');
        sentenceDiv.className = 'sentence';
        sentenceDiv.dataset.index = index;
        
        const words = sentence.split(/\s+/);
        const wordsHtml = words.map(word => {
            const cleanWord = word.replace(/[^a-zA-Z]/g, '').toLowerCase();
            if (cleanWord.length > 2) {
                return `<span class="word" data-word="${cleanWord}">${word}</span>`;
            }
            return word;
        }).join(' ');
        
        sentenceDiv.innerHTML = `
            ${wordsHtml}
            <div class="sentence-controls">
                <button class="play-sentence-btn" onclick="playSentence(${index})">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        `;
        
        // 添加单词点击事件
        sentenceDiv.querySelectorAll('.word').forEach(wordSpan => {
            wordSpan.addEventListener('click', (e) => {
                const word = e.target.dataset.word;
                selectWord(word, e.target);
            });
        });
        
        container.appendChild(sentenceDiv);
    });
}

// 显示翻译
function displayTranslation() {
    const container = document.getElementById('translation-text');
    const translationSentences = currentTranslation.split(/[。！？]/);
    
    container.innerHTML = '';
    translationSentences.forEach((sentence, index) => {
        if (sentence.trim()) {
            const sentenceDiv = document.createElement('div');
            sentenceDiv.className = 'sentence';
            sentenceDiv.textContent = sentence.trim() + '。';
            container.appendChild(sentenceDiv);
        }
    });
}

// 选择单词
function selectWord(word, element) {
    // 清除之前的选择
    document.querySelectorAll('.word.selected').forEach(w => {
        w.classList.remove('selected');
    });
    
    // 选择当前单词
    element.classList.add('selected');
    selectedWord = word;
    
    // 调用新的API获取单词详情
    fetchWordDetailsFromAPI(word);
}

// 调用API获取单词详情
function fetchWordDetailsFromAPI(word) {
    // 显示加载状态
    displayWordDetails(word);

    // 调用后端API
    fetch(`/api/ai/word-details/${encodeURIComponent(word)}`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        console.log('API响应:', result);
        if (result.success && result.data) {
            renderWordDetails(result.data);
        } else {
            console.error('API返回错误:', result);
            showWordError(word, result.error || '获取单词详情失败');
        }
    })
    .catch(error => {
        console.error('单词查询失败:', error);
        showWordError(word, error.message || '网络请求失败');
    });
}

// 处理单词点击事件
function handleWordClick(word) {
    selectedWord = word;
    
    // 显示单词详情面板
    const detailsPanel = document.querySelector('.word-details-panel');
    if (detailsPanel) {
        detailsPanel.style.display = 'block';
    }
    
    // 获取单词详情
    fetchWordDetails(word);
}

// 获取单词详情
function fetchWordDetails(word) {
    // 显示加载状态
    const detailsContent = document.getElementById('word-details-content');
    detailsContent.innerHTML = '<div class="loading">正在加载单词详情...</div>';
    
    // 调用后端API获取单词详情
    fetch(`/api/ai/word-details/${encodeURIComponent(word)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            displayWordDetails(data);
        })
        .catch(error => {
            console.error('Error fetching word details:', error);
            // 使用模拟数据作为后备
            const mockDetails = {
                word: word,
                pronunciation: '/wɜːrd/',
                translation: '单词',
                grammar: '名词',
                examples: [
                    'This is a word.',
                    'Words have meanings.'
                ]
            };
            displayWordDetails(mockDetails);
        });
}

// 显示单词详情
function displayWordDetails(wordData) {
    const container = document.getElementById('word-details-content');
    
    // 如果传入的是字符串，则作为单词名处理
    if (typeof wordData === 'string') {
        const word = wordData;
        // 显示加载状态
        container.innerHTML = `
            <div class="word-info">
                <div class="word-title">
                    ${word}
                    <button class="play-btn" onclick="playWord('${word}')">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
                <div class="word-meanings">
                    <h4>释义</h4>
                    <div class="meaning-item">
                        正在加载单词释义...
                    </div>
                </div>
            </div>
        `;
        
        // 调用单词查询API
        fetch('/api/ai/word-lookup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ word: word })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data) {
                const data = result.data;
                renderWordDetails(data);
            } else {
                showWordError(word);
            }
        })
        .catch(error => {
            console.error('单词查询失败:', error);
            showWordError(word);
        });
    } else {
        // 直接渲染单词数据
        renderWordDetails(wordData);
    }
}

// 渲染单词详情
function renderWordDetails(wordData) {
    const container = document.getElementById('word-details-content');
    
    // 如果wordData是字符串，尝试解析为JSON
    if (typeof wordData === 'string') {
        try {
            wordData = JSON.parse(wordData);
        } catch (e) {
            console.error('解析单词数据失败:', e);
            showWordError(selectedWord);
            return;
        }
    }
    
    const word = wordData.word || selectedWord;
    const phonetic = wordData.phonetic || '';
    const translation = wordData.translation || '';
    const partOfSpeech = wordData.partOfSpeech || '';
    const examples = wordData.examples || [];
    const phrases = wordData.phrases || [];
    
    container.innerHTML = `
        <div class="word-info">
            <div class="word-title">
                ${word}
                <button class="play-btn" onclick="playWord('${word}')">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <div class="word-phonetic">
                ${phonetic}
                ${phonetic ? `<button class="play-btn" onclick="playWord('${word}')"><i class="fas fa-volume-up"></i></button>` : ''}
            </div>
            <div class="word-translation">
                <h4>翻译</h4>
                <div class="translation-item">
                    <strong>${partOfSpeech}</strong> ${translation}
                </div>
            </div>
            <div class="word-examples">
                <h4>例句</h4>
                ${examples.map(example => `
                    <div class="example-item">
                        <div class="example-en">${example.english || ''}</div>
                        <div class="example-zh">${example.chinese || ''}</div>
                    </div>
                `).join('')}
            </div>
            <div class="word-phrases">
                <h4>常用短语</h4>
                ${phrases.map(phrase => `
                    <div class="phrase-item">${phrase}</div>
                `).join('')}
            </div>
        </div>
    `;
}

// 显示单词查询错误
function showWordError(word, errorMessage = '暂时无法获取该单词的详细信息') {
    const container = document.getElementById('word-details-content');

    // 显示错误信息
    container.innerHTML = `
        <div class="word-info">
            <div class="word-title">
                ${word}
                <button class="play-btn" onclick="playWord('${word}')">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <div class="word-error">
                <p>抱歉，${errorMessage}。</p>
                <p>请检查网络连接或稍后重试。</p>
                <button class="retry-btn" onclick="fetchWordDetailsFromAPI('${word}')">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        </div>
    `;
}

// 清除单词详情
function clearWordDetails() {
    const container = document.getElementById('word-details-content');
    container.innerHTML = `
        <div class="word-placeholder">
            <i class="fas fa-mouse-pointer"></i>
            <p>点击单词查看详细信息</p>
        </div>
    `;
}

// 播放单词发音
function playWord(word) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(word);
        utterance.lang = 'en-US';
        utterance.rate = 0.8;
        speechSynthesis.speak(utterance);
    } else {
        showMessage('您的浏览器不支持语音合成', 'error');
    }
}

// 播放句子
function playSentence(index) {
    if (isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
        return;
    }
    
    const sentence = sentences[index].replace(/[^a-zA-Z\s]/g, '');
    const sentenceElement = document.querySelector(`[data-index="${index}"]`);
    
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(sentence);
        utterance.lang = 'en-US';
        utterance.rate = 0.7;
        
        utterance.onstart = () => {
            isPlaying = true;
            currentSentenceIndex = index;
            sentenceElement.classList.add('playing');
        };
        
        utterance.onend = () => {
            isPlaying = false;
            currentSentenceIndex = -1;
            sentenceElement.classList.remove('playing');
        };
        
        speechSynthesis.speak(utterance);
    } else {
        showMessage('您的浏览器不支持语音合成', 'error');
    }
}

// 播放全部句子
function playAllSentences() {
    if (isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
        return;
    }
    
    playNextSentence(0);
}

function playNextSentence(index) {
    if (index >= sentences.length) {
        clearPlayingState();
        return;
    }
    
    const sentence = sentences[index].replace(/[^a-zA-Z\s]/g, '');
    const sentenceElement = document.querySelector(`[data-index="${index}"]`);
    
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(sentence);
        utterance.lang = 'en-US';
        utterance.rate = 0.7;
        
        utterance.onstart = () => {
            isPlaying = true;
            currentSentenceIndex = index;
            sentenceElement.classList.add('playing');
        };
        
        utterance.onend = () => {
            sentenceElement.classList.remove('playing');
            setTimeout(() => {
                playNextSentence(index + 1);
            }, 500);
        };
        
        speechSynthesis.speak(utterance);
    }
}

function clearPlayingState() {
    isPlaying = false;
    currentSentenceIndex = -1;
    document.querySelectorAll('.sentence.playing').forEach(s => {
        s.classList.remove('playing');
    });
}

// 切换翻译显示
function toggleTranslation() {
    const panel = document.querySelector('.translation-panel');
    const btn = document.getElementById('translate-all-btn');
    
    if (panel && btn) {
        if (panel.style.display === 'none') {
            panel.style.display = 'flex';
            btn.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏翻译';
        } else {
            panel.style.display = 'none';
            btn.innerHTML = '<i class="fas fa-eye"></i> 显示翻译';
        }
    }
}

// 调整字体大小
function adjustFontSize() {
    const textContent = document.querySelectorAll('.text-content');
    const currentSize = parseInt(window.getComputedStyle(textContent[0]).fontSize);
    const newSize = currentSize >= 20 ? 16 : currentSize + 2;
    
    textContent.forEach(content => {
        content.style.fontSize = newSize + 'px';
    });
}

// 显示单词模态框
function showWordModal(word) {
    const modal = document.getElementById('word-modal');
    const wordData = mockWordData[word];
    
    if (wordData) {
        document.getElementById('modal-word').textContent = word;
        document.getElementById('modal-phonetic').textContent = wordData.phonetic;
        document.getElementById('modal-meanings').innerHTML = wordData.meanings.map(meaning => `
            <div class="meaning-item">
                <strong>${meaning.type}</strong> ${meaning.definition}
            </div>
        `).join('');
        document.getElementById('modal-examples').innerHTML = wordData.examples.map(example => `
            <div class="example-item">
                <div class="example-en">${example.en}</div>
                <div class="example-zh">${example.zh}</div>
            </div>
        `).join('');
    }
    
    modal.style.display = 'flex';
}

// 关闭单词模态框
function closeWordModal() {
    document.getElementById('word-modal').style.display = 'none';
}

// 关闭单词详情面板
function closeWordDetails() {
    const detailsPanel = document.querySelector('.word-details-panel');
    if (detailsPanel) {
        detailsPanel.style.display = 'none';
    }
    clearWordDetails();
}

// 更新学习进度统计
function updateProgressStats() {
    document.getElementById('total-words').textContent = learningStats.totalWords;
    document.getElementById('mastered-words').textContent = learningStats.masteredWords;
    document.getElementById('study-time').textContent = learningStats.studyTime;
    document.getElementById('study-streak').textContent = learningStats.streak;
    
    // 更新进度条
    const masteredPercentage = (learningStats.masteredWords / learningStats.totalWords) * 100;
    document.getElementById('progress-fill').style.width = masteredPercentage + '%';
    document.getElementById('progress-text').textContent = `${Math.round(masteredPercentage)}% 已掌握`;
}

// 渲染最近学习的单词
function renderRecentWords() {
    const container = document.getElementById('recent-words');
    container.innerHTML = '';
    
    recentWords.forEach(wordItem => {
        const wordDiv = document.createElement('div');
        wordDiv.className = 'word-item';
        wordDiv.innerHTML = `
            <span class="word">${wordItem.word}</span>
            <span class="meaning">${wordItem.meaning}</span>
            <span class="status ${wordItem.status}">
                ${wordItem.status === 'mastered' ? '已掌握' : 
                  wordItem.status === 'learning' ? '学习中' : '新单词'}
            </span>
        `;
        container.appendChild(wordDiv);
    });
}

// 显示加载动画
function showLoading(message = '加载中...') {
    const loading = document.getElementById('loading');
    document.getElementById('loading-text').textContent = message;
    loading.style.display = 'flex';
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // 添加到页面
    document.body.appendChild(messageDiv);
    
    // 设置样式
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.zIndex = '9999';
    messageDiv.style.maxWidth = '300px';
    messageDiv.style.animation = 'slideIn 0.3s ease';
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Esc键关闭模态框
    if (e.key === 'Escape') {
        closeWordModal();
    }
    
    // 空格键播放/暂停
    if (e.key === ' ' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
        e.preventDefault();
        if (currentSentenceIndex >= 0) {
            if (isPlaying) {
                speechSynthesis.cancel();
            } else {
                playSentence(currentSentenceIndex);
            }
        }
    }
});

// 页面可见性变化时暂停语音
document.addEventListener('visibilitychange', function() {
    if (document.hidden && isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
    }
});

// 窗口大小变化时调整布局
window.addEventListener('resize', function() {
    // 移动端适配
    if (window.innerWidth <= 768) {
        document.body.classList.add('mobile');
    } else {
        document.body.classList.remove('mobile');
    }
});

// 初始检查是否为移动端
if (window.innerWidth <= 768) {
    document.body.classList.add('mobile');
}

// PWA支持
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// 导出函数供HTML调用
window.playWord = playWord;
window.playSentence = playSentence;
window.showWordModal = showWordModal;
window.closeWordModal = closeWordModal;