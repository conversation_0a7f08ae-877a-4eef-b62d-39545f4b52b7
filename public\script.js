// 应用状态管理
class AppState {
    constructor() {
        this.state = {
            // 学习内容
            currentText: '',
            currentTranslation: '',
            sentences: [],
            words: [],
            selectedWord: null,

            // 播放状态
            isPlaying: false,
            currentSentenceIndex: -1,

            // 用户认证
            user: null,
            token: localStorage.getItem('authToken'),

            // 学习进度
            learningStats: {
                totalWords: 0,
                masteredWords: 0,
                learningWords: 0,
                newWords: 0,
                studyTime: 0,
                streak: 0
            },

            // 最近学习的单词
            recentWords: []
        };

        this.listeners = new Map();
        this.initializeAuth();
    }

    // 获取状态
    get(key) {
        return key ? this.state[key] : this.state;
    }

    // 设置状态
    set(key, value) {
        const oldValue = this.state[key];
        this.state[key] = value;
        this.notify(key, value, oldValue);
    }

    // 更新状态（合并对象）
    update(updates) {
        Object.keys(updates).forEach(key => {
            this.set(key, updates[key]);
        });
    }

    // 监听状态变化
    subscribe(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, []);
        }
        this.listeners.get(key).push(callback);

        // 返回取消订阅函数
        return () => {
            const callbacks = this.listeners.get(key);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        };
    }

    // 通知监听器
    notify(key, newValue, oldValue) {
        const callbacks = this.listeners.get(key) || [];
        callbacks.forEach(callback => {
            try {
                callback(newValue, oldValue);
            } catch (error) {
                console.error('State listener error:', error);
            }
        });
    }

    // 初始化认证状态
    async initializeAuth() {
        if (this.state.token) {
            try {
                const user = await this.fetchCurrentUser();
                this.set('user', user);
                await this.loadUserProgress();
            } catch (error) {
                console.warn('Failed to load user:', error);
                this.clearAuth();
            }
        }
    }

    // 设置认证信息
    setAuth(user, token) {
        this.set('user', user);
        this.set('token', token);
        localStorage.setItem('authToken', token);
    }

    // 清除认证信息
    clearAuth() {
        this.set('user', null);
        this.set('token', null);
        localStorage.removeItem('authToken');
    }

    // 获取当前用户
    async fetchCurrentUser() {
        const response = await fetch('/api/auth/me', {
            headers: {
                'Authorization': `Bearer ${this.state.token}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch user');
        }

        return await response.json();
    }

    // 加载用户学习进度
    async loadUserProgress() {
        try {
            const response = await fetch('/api/progress', {
                headers: {
                    'Authorization': `Bearer ${this.state.token}`
                }
            });

            if (response.ok) {
                const progress = await response.json();
                this.set('learningStats', progress.stats);
                this.set('recentWords', progress.recentWords);
            }
        } catch (error) {
            console.warn('Failed to load progress:', error);
        }
    }
}

// 创建全局状态实例
const appState = new AppState();

// 兼容性：保持原有的全局变量（逐步迁移）
let currentText = '';
let currentTranslation = '';
let sentences = [];
let words = [];
let selectedWord = null;
let isPlaying = false;
let currentSentenceIndex = -1;

// 已移除模拟数据，现在完全依赖API调用

// 学习进度数据
let learningStats = {
    totalWords: 156,
    masteredWords: 89,
    learningWords: 45,
    newWords: 22,
    studyTime: 127, // 分钟
    streak: 7 // 连续学习天数
};

let recentWords = [
    { word: "innovation", meaning: "创新", status: "mastered" },
    { word: "sustainable", meaning: "可持续的", status: "learning" },
    { word: "biodiversity", meaning: "生物多样性", status: "new" },
    { word: "artificial", meaning: "人工的", status: "learning" },
    { word: "revolution", meaning: "革命", status: "mastered" }
];

// DOM 加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupEventListeners();
    updateProgressStats();
    renderRecentWords();
    showTab('input');
}

function setupEventListeners() {
    // 导航按钮
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tab = e.target.dataset.tab;
            showTab(tab);
        });
    });

    // 输入方法切换
    document.querySelectorAll('.method-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const method = e.target.dataset.method;
            switchInputMethod(method);
        });
    });

    // 文本输入处理
    document.getElementById('analyze-btn').addEventListener('click', processManualText);
    document.getElementById('search-btn').addEventListener('click', performSearch);
    document.getElementById('search-query').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 文件上传
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleFileDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // 学习模式控制
    document.getElementById('play-all-btn').addEventListener('click', playAllSentences);
    document.getElementById('translate-all-btn').addEventListener('click', toggleTranslation);
    document.getElementById('font-size-btn').addEventListener('click', adjustFontSize);

    // 模态框关闭
    document.getElementById('close-modal').addEventListener('click', closeWordModal);
    document.getElementById('word-modal').addEventListener('click', (e) => {
        if (e.target.id === 'word-modal') {
            closeWordModal();
        }
    });
    
    // 单词详情面板关闭
    document.getElementById('close-word-details').addEventListener('click', closeWordDetails);
}

// 标签页切换
function showTab(tabName) {
    // 更新导航按钮状态
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });

    // 显示对应内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName + '-tab').classList.add('active');
}

// 输入方法切换
function switchInputMethod(method) {
    // 更新按钮状态
    document.querySelectorAll('.method-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.method === method) {
            btn.classList.add('active');
        }
    });

    // 显示对应输入方式
    document.querySelectorAll('.input-method').forEach(methodDiv => {
        methodDiv.classList.remove('active');
    });
    document.getElementById(method + '-input').classList.add('active');
}

// 处理手动输入的文本
function processManualText() {
    const text = document.getElementById('text-input').value.trim();
    if (!text) {
        showMessage('请输入要学习的英文文本', 'error');
        return;
    }
    
    processText(text);
}

// 执行AI搜索
async function performSearch() {
    const query = document.getElementById('search-query').value.trim();
    if (!query) {
        showMessage('请输入搜索关键词', 'error');
        return;
    }

    showLoading('正在搜索相关文章...');
    
    try {
        const response = await fetch('/api/ai/search-articles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                query: query,
                difficulty: 'intermediate'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 将API返回的文章转换为搜索结果格式
            const results = [{
                title: data.data.title || `关于"${query}"的文章`,
                content: data.data.content,
                source: 'AI生成'
            }];
            displaySearchResults(results);
        } else {
            showMessage('搜索失败: ' + (data.error || '未知错误'), 'error');
            displaySearchResults([]);
        }
    } catch (error) {
        console.error('搜索错误:', error);
        showMessage('网络错误，请检查连接后重试', 'error');
        displaySearchResults([]);
    } finally {
        hideLoading();
    }
}

// 显示搜索结果
function displaySearchResults(results) {
    const container = document.getElementById('search-results');
    container.innerHTML = '';

    if (results.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>暂无搜索结果</p>
                <p>请尝试其他关键词或检查网络连接</p>
            </div>
        `;
        return;
    }

    results.forEach((result, index) => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'search-result-item';
        resultDiv.innerHTML = `
            <h4>${result.title}</h4>
            <p>${result.content}</p>
            <small>来源: ${result.source}</small>
        `;
        resultDiv.addEventListener('click', () => {
            processText(result.content);
            showMessage('文章已加载到学习模式', 'success');
        });
        container.appendChild(resultDiv);
    });
}

// 文件拖拽处理
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

// 处理上传的文件
function processFile(file) {
    if (!file.type.startsWith('text/')) {
        showMessage('请上传文本文件', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const text = e.target.result;
        processText(text);
        showMessage('文件已成功加载', 'success');
    };
    reader.readAsText(file);
}

// 处理文本内容
async function processText(text) {
    showLoading('正在处理文本...');

    try {
        currentText = text;
        sentences = splitIntoSentences(text);
        words = extractWords(text);

        // 翻译文本
        updateLoadingMessage('正在翻译文本...');
        currentTranslation = await translateText(text);

        // 预处理所有单词翻译
        updateLoadingMessage('正在预处理单词翻译...');
        await preprocessWordTranslations(words);

        displayLearningContent();
        hideLoading();
        showTab('learning');
        showMessage('文本处理完成，开始学习吧！', 'success');
    } catch (error) {
         console.error('文本处理错误:', error);
         hideLoading();
         showMessage('文本处理失败，请重试', 'error');
     }
 }

// 预处理单词翻译
async function preprocessWordTranslations(words) {
    return new Promise((resolve, reject) => {
        // 去重并过滤有效单词
        const uniqueWords = [...new Set(words.filter(word =>
            word && word.length > 1 && /^[a-z]+$/.test(word)
        ))];

        if (uniqueWords.length === 0) {
            resolve();
            return;
        }

        console.log(`开始预处理 ${uniqueWords.length} 个单词的翻译`);

        // 显示限流提示
        if (uniqueWords.length > 10) {
            showMessage(`检测到 ${uniqueWords.length} 个单词需要翻译。为避免API限流，系统将以较慢速度处理，预计需要 ${Math.ceil(uniqueWords.length * 1.5 / 60)} 分钟，请耐心等待。`, 'info');
        }

        // 创建进度显示
        const progressContainer = createProgressDisplay();

        // 开始预处理
        translationPreprocessor.preprocessAllTranslations(
            uniqueWords,
            // 进度回调
            (progress) => {
                updateProgressDisplay(progressContainer, progress);
            },
            // 完成回调
            (success, result) => {
                removeProgressDisplay(progressContainer);

                if (success) {
                    console.log('✅ 所有单词翻译预处理完成');
                    resolve();
                } else {
                    console.log(`⚠️ 翻译预处理完成，但有 ${result.failed} 个单词失败`);
                    resolve(); // 即使有失败也继续，不阻塞学习模式
                }
            }
        );
    });
}

// 创建进度显示
function createProgressDisplay() {
    const container = document.createElement('div');
    container.className = 'translation-progress';
    container.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10001;
            min-width: 300px;
            text-align: center;
        ">
            <div style="font-weight: bold; margin-bottom: 15px;">预处理单词翻译</div>
            <div class="progress-bar" style="
                width: 100%;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 10px;
            ">
                <div class="progress-fill" style="
                    height: 100%;
                    background: #4CAF50;
                    width: 0%;
                    transition: width 0.3s ease;
                "></div>
            </div>
            <div class="progress-text" style="font-size: 14px; color: #666;">
                准备中...
            </div>
        </div>
    `;

    document.body.appendChild(container);
    return container;
}

// 更新进度显示
function updateProgressDisplay(container, progress) {
    const progressFill = container.querySelector('.progress-fill');
    const progressText = container.querySelector('.progress-text');

    progressFill.style.width = `${progress.progress}%`;

    let statusText = `${progress.completed}/${progress.total} 个单词 (成功: ${progress.processed}, 失败: ${progress.failed})`;

    // 如果有待处理的单词，显示处理速度信息
    if (progress.pending > 0) {
        statusText += ` | 剩余: ${progress.pending} 个`;
        statusText += ` | 为避免限流，处理速度较慢，请耐心等待...`;
    }

    progressText.textContent = statusText;
}

// 移除进度显示
function removeProgressDisplay(container) {
    if (container && container.parentElement) {
        container.remove();
    }
}

// 更新加载消息
function updateLoadingMessage(message) {
    const loadingText = document.querySelector('.loading-text');
    if (loadingText) {
        loadingText.textContent = message;
    }
}

// 分割句子
function splitIntoSentences(text) {
    return text.match(/[^.!?]+[.!?]+/g) || [text];
}

// 提取单词
function extractWords(text) {
    return text.toLowerCase().match(/\b[a-z]+\b/g) || [];
}

// 翻译文本
async function translateText(text) {
    try {
        const response = await fetch('/api/ai/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                text: text,
                from: 'en',
                to: 'zh'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            return data.data.translation;
        } else {
            console.error('翻译失败:', data.error);
            // 降级到模拟翻译
            return `[翻译] ${text}`;
        }
    } catch (error) {
        console.error('翻译API调用错误:', error);
        // 降级到模拟翻译
        return `[翻译] ${text}`;
    }
}

// 显示学习内容
function displayLearningContent() {
    displayOriginalText();
    displayTranslation();
    clearWordDetails();
}

// 显示原文
function displayOriginalText() {
    const container = document.getElementById('original-text');
    container.innerHTML = '';

    sentences.forEach((sentence, index) => {
        const sentenceDiv = document.createElement('div');
        sentenceDiv.className = 'sentence';
        sentenceDiv.dataset.index = index;
        
        const words = sentence.split(/\s+/);
        const wordsHtml = words.map(word => {
            const cleanWord = word.replace(/[^a-zA-Z]/g, '').toLowerCase();
            if (cleanWord.length > 2) {
                return `<span class="word hoverable-word" data-word="${cleanWord}">${word}</span>`;
            }
            return word;
        }).join(' ');
        
        sentenceDiv.innerHTML = `
            ${wordsHtml}
            <div class="sentence-controls">
                <button class="play-sentence-btn" onclick="playSentence(${index})">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        `;
        
        // 添加单词事件监听器
        sentenceDiv.querySelectorAll('.word').forEach(wordSpan => {
            const word = wordSpan.dataset.word;

            // 点击事件
            wordSpan.addEventListener('click', (e) => {
                selectWord(word, e.target);
            });

            // 鼠标进入事件
            wordSpan.addEventListener('mouseenter', (e) => {
                showWordTooltip(e, word);
            });

            // 鼠标离开事件
            wordSpan.addEventListener('mouseleave', () => {
                hideWordTooltip();
            });
        });
        
        container.appendChild(sentenceDiv);
    });
}

// 显示翻译
function displayTranslation() {
    const container = document.getElementById('translation-text');
    const translationSentences = currentTranslation.split(/[。！？]/);
    
    container.innerHTML = '';
    translationSentences.forEach((sentence, index) => {
        if (sentence.trim()) {
            const sentenceDiv = document.createElement('div');
            sentenceDiv.className = 'sentence';
            sentenceDiv.textContent = sentence.trim() + '。';
            container.appendChild(sentenceDiv);
        }
    });
}

// 选择单词
function selectWord(word, element) {
    // 清除之前的选择
    document.querySelectorAll('.word.selected').forEach(w => {
        w.classList.remove('selected');
    });
    
    // 选择当前单词
    element.classList.add('selected');
    selectedWord = word;
    
    // 调用新的API获取单词详情
    fetchWordDetailsFromAPI(word);
}

// 调用API获取单词详情（带缓存）
async function fetchWordDetailsFromAPI(word) {
    try {
        // 显示加载状态
        displayWordDetails(word);

        // 先检查缓存
        const cachedData = wordCache.get(word);
        if (cachedData) {
            console.log(`从缓存获取单词详情: ${word}`);
            renderWordDetails(cachedData);
            return;
        }

        // 从API获取
        console.log(`从API获取单词详情: ${word}`);
        const response = await fetch(`/api/ai/word-details/${encodeURIComponent(word)}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('API响应:', result);

        if (result.success && result.data) {
            // 存入缓存
            wordCache.set(word, result.data);
            renderWordDetails(result.data);
        } else {
            console.error('API返回错误:', result);
            showWordError(word, result.error || '获取单词详情失败');
        }
    } catch (error) {
        console.error('单词查询失败:', error);
        showWordError(word, error.message || '网络请求失败');
    }
}

// 处理单词点击事件
function handleWordClick(word) {
    selectedWord = word;
    
    // 显示单词详情面板
    const detailsPanel = document.querySelector('.word-details-panel');
    if (detailsPanel) {
        detailsPanel.style.display = 'block';
    }
    
    // 获取单词详情
    fetchWordDetails(word);
}

// 获取单词详情
function fetchWordDetails(word) {
    // 显示加载状态
    const detailsContent = document.getElementById('word-details-content');
    detailsContent.innerHTML = '<div class="loading">正在加载单词详情...</div>';
    
    // 调用后端API获取单词详情
    fetch(`/api/ai/word-details/${encodeURIComponent(word)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            displayWordDetails(data);
        })
        .catch(error => {
            console.error('Error fetching word details:', error);
            // 使用模拟数据作为后备
            const mockDetails = {
                word: word,
                pronunciation: '/wɜːrd/',
                translation: '单词',
                grammar: '名词',
                examples: [
                    'This is a word.',
                    'Words have meanings.'
                ]
            };
            displayWordDetails(mockDetails);
        });
}

// 显示单词详情
function displayWordDetails(wordData) {
    const container = document.getElementById('word-details-content');
    
    // 如果传入的是字符串，则作为单词名处理
    if (typeof wordData === 'string') {
        const word = wordData;
        // 显示加载状态
        container.innerHTML = `
            <div class="word-info">
                <div class="word-title">
                    ${word}
                    <button class="play-btn" onclick="playWord('${word}')">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
                <div class="word-meanings">
                    <h4>释义</h4>
                    <div class="meaning-item">
                        正在加载单词释义...
                    </div>
                </div>
            </div>
        `;
        
        // 调用单词查询API
        fetch('/api/ai/word-lookup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ word: word })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            if (result.success && result.data) {
                const data = result.data;
                renderWordDetails(data);
            } else {
                showWordError(word, result.error || result.message || '单词查询失败');
            }
        })
        .catch(error => {
            console.error('单词查询失败:', error);
            showWordError(word, error.message || '网络请求失败');
        });
    } else {
        // 直接渲染单词数据
        renderWordDetails(wordData);
    }
}

// 渲染单词详情
function renderWordDetails(wordData) {
    const container = document.getElementById('word-details-content');
    
    // 如果wordData是字符串，尝试解析为JSON
    if (typeof wordData === 'string') {
        try {
            wordData = JSON.parse(wordData);
        } catch (e) {
            console.error('解析单词数据失败:', e);
            showWordError(selectedWord);
            return;
        }
    }
    
    const word = wordData.word || selectedWord;
    const phonetic = wordData.phonetic || '';
    const translation = wordData.translation || '';
    const partOfSpeech = wordData.partOfSpeech || '';
    const examples = wordData.examples || [];
    const phrases = wordData.phrases || [];

    // 处理meanings数据结构
    let meanings = [];
    if (wordData.meanings && Array.isArray(wordData.meanings)) {
        meanings = wordData.meanings;
    } else if (translation) {
        meanings = [{
            type: partOfSpeech || 'noun',
            definitions: [{
                definition: translation,
                example: examples[0]?.english || ''
            }]
        }];
    }
    
    container.innerHTML = `
        <div class="word-info">
            <div class="word-title">
                ${word}
                <button class="play-btn" onclick="playWord('${word}')">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <div class="word-phonetic">
                ${phonetic}
                ${phonetic ? `<button class="play-btn" onclick="playWord('${word}')"><i class="fas fa-volume-up"></i></button>` : ''}
            </div>
            <div class="word-meanings">
                <h4>释义</h4>
                ${meanings.map(meaning => `
                    <div class="meaning-item">
                        <strong>${meaning.type || partOfSpeech}</strong>
                        ${meaning.definitions ? meaning.definitions.map(def => def.definition).join('; ') : translation}
                    </div>
                `).join('')}
            </div>
            <div class="word-examples">
                <h4>例句</h4>
                ${examples.length > 0 ? examples.map(example => `
                    <div class="example-item">
                        <div class="example-en">${example.english || example}</div>
                        ${example.chinese ? `<div class="example-zh">${example.chinese}</div>` : ''}
                    </div>
                `).join('') : '<div class="no-examples">暂无例句</div>'}
            </div>
            <div class="word-phrases">
                <h4>常用短语</h4>
                ${phrases.length > 0 ? phrases.map(phrase => `
                    <div class="phrase-item">${phrase}</div>
                `).join('') : '<div class="no-phrases">暂无短语</div>'}
            </div>
        </div>
    `;
}

// 显示单词查询错误
function showWordError(word, errorMessage = '暂时无法获取该单词的详细信息') {
    const container = document.getElementById('word-details-content');

    // 显示错误信息
    container.innerHTML = `
        <div class="word-info">
            <div class="word-title">
                ${word}
                <button class="play-btn" onclick="playWord('${word}')">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <div class="word-error">
                <p>抱歉，${errorMessage}。</p>
                <p>请检查网络连接或稍后重试。</p>
                <button class="retry-btn" onclick="fetchWordDetailsFromAPI('${word}')">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        </div>
    `;
}

// 清除单词详情
function clearWordDetails() {
    const container = document.getElementById('word-details-content');
    container.innerHTML = `
        <div class="word-placeholder">
            <i class="fas fa-mouse-pointer"></i>
            <p>点击单词查看详细信息</p>
        </div>
    `;
}

// 播放单词发音
function playWord(word) {
    if ('speechSynthesis' in window) {
        // 停止当前播放
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(word);
        utterance.lang = 'en-US';
        utterance.rate = 0.8;
        utterance.pitch = 1;
        utterance.volume = 1;

        // 等待语音引擎准备就绪
        setTimeout(() => {
            speechSynthesis.speak(utterance);
        }, 100);

        console.log(`播放单词发音: ${word}`);
    } else {
        showMessage('您的浏览器不支持语音合成', 'error');
    }
}

// 单词缓存系统
class WordCache {
    constructor() {
        this.cache = new Map();
        this.maxSize = 500; // 最大缓存500个单词
        this.ttl = 30 * 60 * 1000; // 30分钟过期
    }

    set(word, data, forceSet = false) {
        const isHighQuality = this.isHighQualityData(data);

        // 如果不是强制设置，检查数据质量
        if (!forceSet && !isHighQuality) {
            console.log(`跳过低质量数据缓存: ${word}`);
            return;
        }

        // 如果已有缓存，比较质量
        const existing = this.get(word);
        if (existing && this.isHighQualityData(existing) && !isHighQuality) {
            console.log(`保留现有高质量缓存: ${word}`);
            return;
        }

        // 如果缓存满了，删除最老的条目
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(word, {
            data: data,
            timestamp: Date.now(),
            isHighQuality: isHighQuality
        });

        if (isHighQuality) {
            console.log(`高质量数据已缓存: ${word}`);
        } else {
            console.log(`临时缓存低质量数据: ${word}`);
        }
    }

    // 临时存储低质量数据（用于显示，但不影响高质量缓存）
    setTemporary(word, data) {
        const existing = this.get(word);
        // 只有在没有现有缓存或现有缓存也是低质量时才临时存储
        if (!existing || !this.isHighQualityData(existing)) {
            this.set(word, data, true); // 强制设置
        }
    }

    // 检查数据质量
    isHighQualityData(data) {
        if (!data || !data.translation) return false;

        const translation = data.translation.toLowerCase();
        const word = data.word ? data.word.toLowerCase() : '';

        // 检查是否为低质量的通用回复
        const lowQualityPatterns = [
            '这是一个英语单词',
            '这是一个常用的英语单词',
            '的基本释义',
            '的中文释义',
            'a common english word',
            'basic meaning',
            'common word'
        ];

        for (const pattern of lowQualityPatterns) {
            if (translation.includes(pattern.toLowerCase())) {
                return false;
            }
        }

        // 检查翻译是否过于简单或重复单词本身
        if (translation.length < 2 || translation === word) {
            return false;
        }

        // 检查是否有具体的中文翻译内容
        const hasChineseContent = /[\u4e00-\u9fff]/.test(translation);
        if (!hasChineseContent) {
            return false;
        }

        return true;
    }

    get(word) {
        const cached = this.cache.get(word);
        if (!cached) return null;

        // 检查是否过期（低质量数据过期时间更短）
        const ttl = cached.isHighQuality ? this.ttl : this.ttl / 6; // 低质量数据5分钟过期
        if (Date.now() - cached.timestamp > ttl) {
            this.cache.delete(word);
            return null;
        }

        return cached.data;
    }

    has(word) {
        return this.get(word) !== null;
    }

    clear() {
        this.cache.clear();
    }
}

// 创建全局单词缓存实例
const wordCache = new WordCache();

// 翻译预处理管理器
class TranslationPreprocessor {
    constructor() {
        this.pendingWords = new Set();
        this.processedWords = new Set();
        this.failedWords = new Set();
        this.maxRetries = 3;
        this.retryDelay = 3000; // 增加重试延迟
        this.batchSize = 1; // 一次只处理1个单词，避免限流
        this.maxConcurrent = 1; // 最大并发请求数设为1
        this.requestDelay = 1500; // 请求之间的延迟（1.5秒）
        this.isProcessing = false;
        this.onProgressCallback = null;
        this.onCompleteCallback = null;
        this.activeRequests = new Set(); // 跟踪活跃请求
        this.timeouts = new Set(); // 跟踪所有定时器
        this.abortController = null; // 用于取消请求
    }

    // 清理所有资源
    cleanup() {
        // 取消所有进行中的请求
        if (this.abortController) {
            this.abortController.abort();
        }

        // 清理所有定时器
        this.timeouts.forEach(timeoutId => clearTimeout(timeoutId));
        this.timeouts.clear();

        // 清理活跃请求记录
        this.activeRequests.clear();

        // 重置状态
        this.isProcessing = false;
        this.onProgressCallback = null;
        this.onCompleteCallback = null;

        console.log('🧹 翻译预处理器资源已清理');
    }

    // 预处理所有单词翻译
    async preprocessAllTranslations(words, onProgress, onComplete) {
        // 清理之前的资源
        this.cleanup();

        this.onProgressCallback = onProgress;
        this.onCompleteCallback = onComplete;
        this.abortController = new AbortController();

        // 重置状态
        this.pendingWords.clear();
        this.processedWords.clear();
        this.failedWords.clear();

        // 过滤出需要处理的单词（没有高质量缓存的）
        const wordsToProcess = words.filter(word => {
            const cached = wordCache.get(word);
            return !cached || !wordCache.isHighQualityData(cached);
        });

        if (wordsToProcess.length === 0) {
            console.log('所有单词都已有高质量翻译缓存');
            this.onCompleteCallback && this.onCompleteCallback(true);
            return;
        }

        console.log(`开始预处理 ${wordsToProcess.length} 个单词的翻译`);

        // 添加到待处理队列
        wordsToProcess.forEach(word => this.pendingWords.add(word));

        // 开始批量处理
        this.isProcessing = true;
        await this.processBatch();
    }

    // 批量处理单词（带并发控制）
    async processBatch() {
        if (!this.isProcessing || this.pendingWords.size === 0) {
            this.finishProcessing();
            return;
        }

        // 控制并发数量
        const availableSlots = this.maxConcurrent - this.activeRequests.size;
        if (availableSlots <= 0) {
            // 等待一些请求完成后再继续
            const timeoutId = setTimeout(() => {
                this.timeouts.delete(timeoutId);
                this.processBatch();
            }, 1000);
            this.timeouts.add(timeoutId);
            return;
        }

        // 获取当前批次的单词（不超过可用槽位）
        const currentBatch = Array.from(this.pendingWords).slice(0, Math.min(availableSlots, this.batchSize));

        // 并行处理当前批次
        const promises = currentBatch.map(word => this.processWord(word));
        await Promise.allSettled(promises);

        // 更新进度
        this.updateProgress();

        // 继续处理下一批（增加延迟避免限流）
        if (this.isProcessing && this.pendingWords.size > 0) {
            const timeoutId = setTimeout(() => {
                this.timeouts.delete(timeoutId);
                this.processBatch();
            }, this.requestDelay); // 使用配置的请求延迟
            this.timeouts.add(timeoutId);
        } else {
            this.finishProcessing();
        }
    }

    // 处理单个单词
    async processWord(word, retryCount = 0) {
        // 检查是否已被取消
        if (this.abortController?.signal.aborted) {
            return;
        }

        // 添加到活跃请求跟踪
        this.activeRequests.add(word);

        try {
            console.log(`处理单词: ${word} (尝试 ${retryCount + 1})`);

            // 等待限流控制
            await requestLimiter.waitForNextRequest();

            const response = await fetch(`/api/ai/word-details/${encodeURIComponent(word)}`, {
                signal: this.abortController.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                const data = result.data;

                if (wordCache.isHighQualityData(data)) {
                    // 高质量翻译，存入缓存
                    wordCache.set(word, data);
                    this.pendingWords.delete(word);
                    this.processedWords.add(word);
                    console.log(`✅ 成功处理: ${word}`);
                } else if (retryCount < this.maxRetries) {
                    // 低质量翻译，重试
                    console.log(`⚠️ 低质量翻译，重试: ${word}`);
                    setTimeout(() => {
                        this.processWord(word, retryCount + 1);
                    }, this.retryDelay);
                } else {
                    // 达到最大重试次数，记录为失败但继续处理
                    this.pendingWords.delete(word);
                    this.failedWords.add(word);
                    console.log(`❌ 达到最大重试次数，跳过单词: ${word}`);
                }
            } else {
                throw new Error(result.error || '获取翻译失败');
            }
        } catch (error) {
            // 检查是否是取消操作
            if (error.name === 'AbortError') {
                console.log(`🚫 请求已取消: ${word}`);
                return;
            }

            console.error(`处理单词失败: ${word}`, error);

            // 检查是否是限流错误
            const isRateLimited = error.message.includes('429') || error.message.includes('Too Many Requests');
            let retryDelay = this.retryDelay;

            if (isRateLimited) {
                // 限流错误，使用更长的延迟
                retryDelay = Math.min(this.retryDelay * (retryCount + 1), 30000); // 最多30秒
                console.log(`⏰ 遇到限流，将在 ${retryDelay/1000} 秒后重试: ${word}`);
            }

            if (retryCount < this.maxRetries) {
                // 安排重试
                const timeoutId = setTimeout(() => {
                    this.timeouts.delete(timeoutId);
                    this.processWord(word, retryCount + 1);
                }, retryDelay);
                this.timeouts.add(timeoutId);
            } else {
                // 最终失败，记录并继续
                this.pendingWords.delete(word);
                this.failedWords.add(word);
                console.log(`❌ 最终失败，跳过单词: ${word}`);
            }
        } finally {
            // 从活跃请求中移除
            this.activeRequests.delete(word);
        }
    }



    // 更新进度
    updateProgress() {
        const total = this.processedWords.size + this.failedWords.size + this.pendingWords.size;
        const completed = this.processedWords.size + this.failedWords.size;
        const progress = total > 0 ? (completed / total) * 100 : 0;

        if (this.onProgressCallback) {
            this.onProgressCallback({
                total,
                completed,
                processed: this.processedWords.size,
                failed: this.failedWords.size,
                pending: this.pendingWords.size,
                progress
            });
        }
    }

    // 完成处理
    finishProcessing() {
        this.isProcessing = false;
        const success = this.failedWords.size === 0;

        console.log(`翻译预处理完成:`);
        console.log(`- 成功: ${this.processedWords.size} 个`);
        console.log(`- 失败: ${this.failedWords.size} 个`);

        if (this.onCompleteCallback) {
            this.onCompleteCallback(success, {
                processed: this.processedWords.size,
                failed: this.failedWords.size,
                failedWords: Array.from(this.failedWords)
            });
        }
    }

    // 停止处理
    stop() {
        this.isProcessing = false;
    }
}

// 全局请求限流控制
class RequestLimiter {
    constructor() {
        this.lastRequestTime = 0;
        this.minInterval = 1000; // 最小请求间隔1秒
    }

    async waitForNextRequest() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;

        if (timeSinceLastRequest < this.minInterval) {
            const waitTime = this.minInterval - timeSinceLastRequest;
            console.log(`⏰ 限流等待 ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastRequestTime = Date.now();
    }
}

// 创建全局实例
const translationPreprocessor = new TranslationPreprocessor();
const requestLimiter = new RequestLimiter();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    console.log('🧹 页面卸载，清理所有资源');
    translationPreprocessor.cleanup();
    hideWordTooltip();
});

// 页面隐藏时暂停处理
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('⏸️ 页面隐藏，暂停翻译处理');
        // 可以选择暂停处理以节省资源
    } else {
        console.log('▶️ 页面可见，恢复翻译处理');
    }
});



// 翻译重试管理器
class TranslationRetryManager {
    constructor() {
        this.retryQueue = new Map(); // 存储需要重试的单词
        this.maxRetries = 3; // 最大重试次数
        this.retryDelay = 2000; // 重试延迟（毫秒）
        this.isRetrying = false;
    }

    // 添加单词到重试队列
    addToRetry(word) {
        if (!this.retryQueue.has(word)) {
            this.retryQueue.set(word, {
                attempts: 0,
                lastAttempt: Date.now()
            });
            console.log(`添加单词到重试队列: ${word}`);
        }

        // 启动重试处理
        this.processRetryQueue();
    }

    // 处理重试队列
    async processRetryQueue() {
        if (this.isRetrying || this.retryQueue.size === 0) {
            return;
        }

        this.isRetrying = true;

        for (const [word, retryInfo] of this.retryQueue.entries()) {
            // 检查是否超过最大重试次数
            if (retryInfo.attempts >= this.maxRetries) {
                console.log(`单词 ${word} 已达到最大重试次数，移出队列`);
                this.retryQueue.delete(word);
                continue;
            }

            // 检查是否需要等待
            const timeSinceLastAttempt = Date.now() - retryInfo.lastAttempt;
            if (timeSinceLastAttempt < this.retryDelay) {
                continue;
            }

            // 执行重试
            console.log(`重试获取单词翻译: ${word} (第${retryInfo.attempts + 1}次)`);
            retryInfo.attempts++;
            retryInfo.lastAttempt = Date.now();

            try {
                await this.retryWordTranslation(word);
            } catch (error) {
                console.error(`重试翻译失败: ${word}`, error);
            }

            // 添加延迟避免过于频繁的请求
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        this.isRetrying = false;

        // 如果还有待重试的单词，继续处理
        if (this.retryQueue.size > 0) {
            setTimeout(() => this.processRetryQueue(), this.retryDelay);
        }
    }

    // 重试单词翻译
    async retryWordTranslation(word) {
        try {
            const response = await fetch(`/api/ai/word-details/${encodeURIComponent(word)}`);
            const result = await response.json();

            if (result.success && result.data) {
                const data = result.data;

                // 检查数据质量
                if (wordCache.isHighQualityData(data)) {
                    // 高质量数据，存入缓存并移出重试队列
                    wordCache.set(word, data);
                    this.retryQueue.delete(word);
                    console.log(`重试成功，获得高质量翻译: ${word}`);

                    // 如果当前正在显示这个单词的提示框，更新内容
                    if (wordTooltip && currentTooltipWord === word) {
                        updateTooltipContent(word, data);
                    }

                    // 检查是否所有单词都已获得高质量翻译
                    this.checkAllWordsReady();
                } else {
                    console.log(`重试仍获得低质量翻译: ${word}`);
                }
            }
        } catch (error) {
            console.error(`重试API调用失败: ${word}`, error);
        }
    }

    // 检查是否所有单词都已准备好
    checkAllWordsReady() {
        // 获取页面中所有单词
        const allWords = this.getAllWordsFromPage();
        const readyWords = allWords.filter(word => {
            const cached = wordCache.get(word);
            return cached && wordCache.isHighQualityData(cached);
        });

        console.log(`翻译进度: ${readyWords.length}/${allWords.length} 个单词已准备好`);

        // 如果所有单词都准备好了，显示学习模式提示
        if (readyWords.length === allWords.length && allWords.length > 0) {
            this.showLearningModeReady();
        }
    }

    // 获取页面中所有单词
    getAllWordsFromPage() {
        const wordElements = document.querySelectorAll('.word');
        return Array.from(wordElements).map(el => el.textContent.trim().toLowerCase());
    }

    // 显示学习模式准备就绪提示
    showLearningModeReady() {
        // 检查是否已经显示过提示
        if (document.querySelector('.learning-ready-notification')) {
            return;
        }

        const notification = document.createElement('div');
        notification.className = 'learning-ready-notification';
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
            ">
                <div style="font-weight: bold; margin-bottom: 8px;">✅ 翻译准备完成</div>
                <div style="margin-bottom: 10px;">所有单词翻译已准备就绪，现在可以进入学习模式了！</div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                ">关闭</button>
            </div>
        `;

        document.body.appendChild(notification);

        // 5秒后自动消失
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        console.log('🎉 所有单词翻译已准备完成，可以进入学习模式！');
    }
}

// 创建全局重试管理器实例
const retryManager = new TranslationRetryManager();

// 单词悬停提示相关变量
let wordTooltip = null;
let tooltipTimeout = null;
let currentTooltipWord = null;
let activeWordRequest = null; // 跟踪当前活跃的单词请求

// 显示单词悬停提示
function showWordTooltip(event, word) {
    // 清除之前的超时
    if (tooltipTimeout) {
        clearTimeout(tooltipTimeout);
    }

    // 如果是同一个单词，不重复显示
    if (currentTooltipWord === word && wordTooltip) {
        return;
    }

    // 隐藏之前的提示
    hideWordTooltip();

    // 播放单词发音
    playWord(word);

    // 检查缓存，如果有缓存则立即显示，否则延迟显示
    const cachedData = wordCache.get(word);
    const delay = cachedData ? 100 : 200; // 有缓存时快速显示

    tooltipTimeout = setTimeout(() => {
        createWordTooltip(event, word);
    }, delay);
}

// 创建单词提示框
function createWordTooltip(event, word) {
    currentTooltipWord = word;

    // 创建提示框
    wordTooltip = document.createElement('div');
    wordTooltip.className = 'word-tooltip';
    wordTooltip.innerHTML = `
        <div class="tooltip-content">
            <div class="tooltip-word">${word}</div>
            <div class="tooltip-loading">正在获取翻译...</div>
        </div>
    `;

    // 设置位置
    const rect = event.target.getBoundingClientRect();
    wordTooltip.style.left = rect.left + 'px';
    wordTooltip.style.top = (rect.bottom + 5) + 'px';

    document.body.appendChild(wordTooltip);

    // 获取单词翻译
    fetchWordTranslation(word);
}

// 获取单词翻译（优先使用缓存，避免实时API调用）
async function fetchWordTranslation(word) {
    try {
        // 先检查缓存
        const cachedData = wordCache.get(word);
        if (cachedData) {
            console.log(`✅ 从缓存获取单词: ${word}`);
            updateTooltipContent(word, cachedData);
            return;
        }

        // 如果没有缓存，说明预处理可能遗漏了这个单词
        console.log(`⚠️ 缓存中没有找到单词: ${word}，尝试实时获取`);

        // 显示加载状态
        if (wordTooltip && currentTooltipWord === word) {
            updateTooltipContent(word, {
                word: word,
                translation: '正在获取翻译...',
                pronunciation: '',
                examples: []
            });
        }

        // 创建新的请求控制器
        activeWordRequest = new AbortController();

        // 等待限流控制
        await requestLimiter.waitForNextRequest();

        // 从API获取（作为备用方案）
        const response = await fetch(`/api/ai/word-details/${encodeURIComponent(word)}`, {
            signal: activeWordRequest.signal
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
            const data = result.data;

            // 存储到缓存
            if (wordCache.isHighQualityData(data)) {
                wordCache.set(word, data);
                console.log(`✅ 实时获取高质量翻译: ${word}`);
            } else {
                wordCache.setTemporary(word, data);
                console.log(`⚠️ 实时获取低质量翻译: ${word}`);
            }

            // 更新提示框内容
            if (wordTooltip && currentTooltipWord === word) {
                updateTooltipContent(word, data);
            }
        } else {
            throw new Error(result.error || '获取单词详情失败');
        }
    } catch (error) {
        // 检查是否是取消操作
        if (error.name === 'AbortError') {
            console.log(`🚫 单词请求已取消: ${word}`);
            return;
        }

        console.error(`❌ 获取单词翻译失败: ${word}`, error);

        if (wordTooltip && currentTooltipWord === word) {
            updateTooltipContent(word, {
                word: word,
                translation: '翻译获取失败，请稍后重试',
                pronunciation: '',
                examples: []
            });
        }
    } finally {
        // 清理请求引用
        if (activeWordRequest) {
            activeWordRequest = null;
        }
    }
}



// 更新提示框内容
function updateTooltipContent(word, data) {
    if (!wordTooltip || currentTooltipWord !== word) return;

    const translation = data.translation || '';
    const phonetic = data.phonetic || '';
    const partOfSpeech = data.partOfSpeech || '';

    wordTooltip.innerHTML = `
        <div class="tooltip-content">
            <div class="tooltip-word">
                ${word}
                <button class="tooltip-play-btn" onclick="playWord('${word}')">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            ${phonetic ? `<div class="tooltip-phonetic">${phonetic}</div>` : ''}
            <div class="tooltip-translation">
                ${partOfSpeech ? `<strong>${partOfSpeech}</strong> ` : ''}${translation}
            </div>
        </div>
    `;
}

// 隐藏单词悬停提示
function hideWordTooltip() {
    // 立即清除超时
    if (tooltipTimeout) {
        clearTimeout(tooltipTimeout);
        tooltipTimeout = null;
    }

    // 取消活跃的请求
    if (activeWordRequest) {
        activeWordRequest.abort();
        activeWordRequest = null;
    }

    // 立即隐藏提示框
    if (wordTooltip) {
        wordTooltip.remove();
        wordTooltip = null;
        currentTooltipWord = null;
    }
}

// 播放句子
function playSentence(index) {
    if (isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
        return;
    }
    
    const sentence = sentences[index].replace(/[^a-zA-Z\s]/g, '');
    const sentenceElement = document.querySelector(`[data-index="${index}"]`);
    
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(sentence);
        utterance.lang = 'en-US';
        utterance.rate = 0.7;
        
        utterance.onstart = () => {
            isPlaying = true;
            currentSentenceIndex = index;
            sentenceElement.classList.add('playing');
        };
        
        utterance.onend = () => {
            isPlaying = false;
            currentSentenceIndex = -1;
            sentenceElement.classList.remove('playing');
        };
        
        speechSynthesis.speak(utterance);
    } else {
        showMessage('您的浏览器不支持语音合成', 'error');
    }
}

// 播放全部句子
function playAllSentences() {
    if (isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
        return;
    }
    
    playNextSentence(0);
}

function playNextSentence(index) {
    if (index >= sentences.length) {
        clearPlayingState();
        return;
    }
    
    const sentence = sentences[index].replace(/[^a-zA-Z\s]/g, '');
    const sentenceElement = document.querySelector(`[data-index="${index}"]`);
    
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(sentence);
        utterance.lang = 'en-US';
        utterance.rate = 0.7;
        
        utterance.onstart = () => {
            isPlaying = true;
            currentSentenceIndex = index;
            sentenceElement.classList.add('playing');
        };
        
        utterance.onend = () => {
            sentenceElement.classList.remove('playing');
            setTimeout(() => {
                playNextSentence(index + 1);
            }, 500);
        };
        
        speechSynthesis.speak(utterance);
    }
}

function clearPlayingState() {
    isPlaying = false;
    currentSentenceIndex = -1;
    document.querySelectorAll('.sentence.playing').forEach(s => {
        s.classList.remove('playing');
    });
}

// 切换翻译显示
function toggleTranslation() {
    const panel = document.querySelector('.translation-panel');
    const btn = document.getElementById('translate-all-btn');
    
    if (panel && btn) {
        if (panel.style.display === 'none') {
            panel.style.display = 'flex';
            btn.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏翻译';
        } else {
            panel.style.display = 'none';
            btn.innerHTML = '<i class="fas fa-eye"></i> 显示翻译';
        }
    }
}

// 调整字体大小
function adjustFontSize() {
    const textContent = document.querySelectorAll('.text-content');
    const currentSize = parseInt(window.getComputedStyle(textContent[0]).fontSize);
    const newSize = currentSize >= 20 ? 16 : currentSize + 2;
    
    textContent.forEach(content => {
        content.style.fontSize = newSize + 'px';
    });
}

// 显示单词模态框
function showWordModal(word) {
    const modal = document.getElementById('word-modal');
    const wordData = mockWordData[word];
    
    if (wordData) {
        document.getElementById('modal-word').textContent = word;
        document.getElementById('modal-phonetic').textContent = wordData.phonetic;
        document.getElementById('modal-meanings').innerHTML = wordData.meanings.map(meaning => `
            <div class="meaning-item">
                <strong>${meaning.type}</strong> ${meaning.definition}
            </div>
        `).join('');
        document.getElementById('modal-examples').innerHTML = wordData.examples.map(example => `
            <div class="example-item">
                <div class="example-en">${example.en}</div>
                <div class="example-zh">${example.zh}</div>
            </div>
        `).join('');
    }
    
    modal.style.display = 'flex';
}

// 关闭单词模态框
function closeWordModal() {
    document.getElementById('word-modal').style.display = 'none';
}

// 关闭单词详情面板
function closeWordDetails() {
    const detailsPanel = document.querySelector('.word-details-panel');
    if (detailsPanel) {
        detailsPanel.style.display = 'none';
    }
    clearWordDetails();
}

// 更新学习进度统计
function updateProgressStats() {
    document.getElementById('total-words').textContent = learningStats.totalWords;
    document.getElementById('mastered-words').textContent = learningStats.masteredWords;
    document.getElementById('study-time').textContent = learningStats.studyTime;
    document.getElementById('study-streak').textContent = learningStats.streak;
    
    // 更新进度条
    const masteredPercentage = (learningStats.masteredWords / learningStats.totalWords) * 100;
    document.getElementById('progress-fill').style.width = masteredPercentage + '%';
    document.getElementById('progress-text').textContent = `${Math.round(masteredPercentage)}% 已掌握`;
}

// 渲染最近学习的单词
function renderRecentWords() {
    const container = document.getElementById('recent-words');
    container.innerHTML = '';
    
    recentWords.forEach(wordItem => {
        const wordDiv = document.createElement('div');
        wordDiv.className = 'word-item';
        wordDiv.innerHTML = `
            <span class="word">${wordItem.word}</span>
            <span class="meaning">${wordItem.meaning}</span>
            <span class="status ${wordItem.status}">
                ${wordItem.status === 'mastered' ? '已掌握' : 
                  wordItem.status === 'learning' ? '学习中' : '新单词'}
            </span>
        `;
        container.appendChild(wordDiv);
    });
}

// 显示加载动画
function showLoading(message = '加载中...') {
    const loading = document.getElementById('loading');
    document.getElementById('loading-text').textContent = message;
    loading.style.display = 'flex';
}

// 隐藏加载动画
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // 添加到页面
    document.body.appendChild(messageDiv);
    
    // 设置样式
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.zIndex = '9999';
    messageDiv.style.maxWidth = '300px';
    messageDiv.style.animation = 'slideIn 0.3s ease';
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Esc键关闭模态框和提示框
    if (e.key === 'Escape') {
        closeWordModal();
        hideWordTooltip();
    }

    // 空格键播放/暂停
    if (e.key === ' ' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
        e.preventDefault();
        if (currentSentenceIndex >= 0) {
            if (isPlaying) {
                speechSynthesis.cancel();
            } else {
                playSentence(currentSentenceIndex);
            }
        }
    }
});

// 点击其他地方隐藏提示框
document.addEventListener('click', function(e) {
    if (wordTooltip && !e.target.closest('.word-tooltip') && !e.target.closest('.hoverable-word')) {
        hideWordTooltip();
    }
});

// 页面可见性变化时暂停语音
document.addEventListener('visibilitychange', function() {
    if (document.hidden && isPlaying) {
        speechSynthesis.cancel();
        clearPlayingState();
    }
});

// 窗口大小变化时调整布局
window.addEventListener('resize', function() {
    // 移动端适配
    if (window.innerWidth <= 768) {
        document.body.classList.add('mobile');
    } else {
        document.body.classList.remove('mobile');
    }
});

// 初始检查是否为移动端
if (window.innerWidth <= 768) {
    document.body.classList.add('mobile');
}

// PWA支持
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// 导出函数供HTML调用
window.playWord = playWord;
window.playSentence = playSentence;
window.showWordModal = showWordModal;
window.closeWordModal = closeWordModal;