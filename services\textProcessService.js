/**
 * 文本处理服务
 * 提供文本分析、单词提取、句子分割等功能
 */

class TextProcessService {
  /**
   * 将文章分割成句子
   * @param {string} text - 输入文本
   * @returns {Array} 句子数组
   */
  static splitIntoSentences(text) {
    if (!text) return [];
    
    // 使用正则表达式分割句子，保留标点符号
    const sentences = text
      .split(/(?<=[.!?])\s+/)
      .filter(sentence => sentence.trim().length > 0)
      .map((sentence, index) => ({
        id: index + 1,
        text: sentence.trim(),
        wordCount: sentence.trim().split(/\s+/).length
      }));
    
    return sentences;
  }

  /**
   * 提取文本中的单词
   * @param {string} text - 输入文本
   * @returns {Array} 单词数组
   */
  static extractWords(text) {
    if (!text) return [];
    
    // 提取单词，去除标点符号，转换为小写
    const words = text
      .toLowerCase()
      .replace(/[^a-zA-Z\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1)
      .map((word, index) => ({
        id: index + 1,
        text: word,
        length: word.length
      }));
    
    return words;
  }

  /**
   * 统计词频
   * @param {string} text - 输入文本
   * @returns {Object} 词频统计对象
   */
  static getWordFrequency(text) {
    if (!text) return {};
    
    const words = text
      .toLowerCase()
      .replace(/[^a-zA-Z\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1);
    
    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    // 按频率排序，返回前20个最常用的单词
    const sortedWords = Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([word, count]) => ({ word, count }));
    
    return {
      total: words.length,
      unique: Object.keys(frequency).length,
      mostFrequent: sortedWords
    };
  }

  /**
   * 提取关键短语
   * @param {string} text - 输入文本
   * @returns {Array} 关键短语数组
   */
  static extractKeyPhrases(text) {
    if (!text) return [];
    
    // 简单的关键短语提取：查找常见的短语模式
    const phrases = [];
    
    // 提取形容词+名词组合
    const adjNounPattern = /\b([a-zA-Z]+(?:ly|ful|ous|ive|able|ing|ed))\s+([a-zA-Z]+(?:s|es|ies|tion|sion|ment|ness))\b/gi;
    let match;
    while ((match = adjNounPattern.exec(text)) !== null) {
      phrases.push({
        type: 'adjective-noun',
        phrase: match[0],
        position: match.index
      });
    }
    
    // 提取动词+名词组合
    const verbNounPattern = /\b([a-zA-Z]+(?:ing|ed|s))\s+([a-zA-Z]+(?:s|es|ies|tion|sion|ment))\b/gi;
    while ((match = verbNounPattern.exec(text)) !== null) {
      phrases.push({
        type: 'verb-noun',
        phrase: match[0],
        position: match.index
      });
    }
    
    // 提取介词短语
    const prepPhrasePattern = /\b(in|on|at|by|for|with|from|to|of|about|through|during|before|after)\s+([a-zA-Z\s]{2,20})\b/gi;
    while ((match = prepPhrasePattern.exec(text)) !== null) {
      phrases.push({
        type: 'prepositional',
        phrase: match[0],
        position: match.index
      });
    }
    
    // 去重并限制数量
    const uniquePhrases = phrases
      .filter((phrase, index, self) => 
        index === self.findIndex(p => p.phrase.toLowerCase() === phrase.phrase.toLowerCase())
      )
      .slice(0, 10);
    
    return uniquePhrases;
  }

  /**
   * 分析文本难度
   * @param {string} text - 输入文本
   * @returns {Object} 难度分析结果
   */
  static analyzeDifficulty(text) {
    if (!text) return { level: 'unknown', score: 0 };
    
    const words = this.extractWords(text);
    const sentences = this.splitIntoSentences(text);
    
    // 计算平均单词长度
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    // 计算平均句子长度
    const avgSentenceLength = sentences.reduce((sum, sentence) => sum + sentence.wordCount, 0) / sentences.length;
    
    // 简单的难度评分算法
    let score = 0;
    
    // 基于单词长度
    if (avgWordLength > 6) score += 2;
    else if (avgWordLength > 4) score += 1;
    
    // 基于句子长度
    if (avgSentenceLength > 20) score += 2;
    else if (avgSentenceLength > 15) score += 1;
    
    // 基于复杂单词比例
    const complexWords = words.filter(word => word.length > 6).length;
    const complexWordRatio = complexWords / words.length;
    if (complexWordRatio > 0.3) score += 2;
    else if (complexWordRatio > 0.2) score += 1;
    
    // 确定难度等级
    let level;
    if (score >= 5) level = 'advanced';
    else if (score >= 3) level = 'intermediate';
    else level = 'beginner';
    
    return {
      level,
      score,
      avgWordLength: Math.round(avgWordLength * 10) / 10,
      avgSentenceLength: Math.round(avgSentenceLength * 10) / 10,
      complexWordRatio: Math.round(complexWordRatio * 100) / 100
    };
  }

  /**
   * 提取文本中的生词（基于常用词列表）
   * @param {string} text - 输入文本
   * @returns {Array} 生词数组
   */
  static extractDifficultWords(text) {
    // 常用词列表（前1000个最常用英语单词的简化版本）
    const commonWords = new Set([
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
      'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
      'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
      'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their',
      'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go',
      'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know',
      'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them',
      'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over',
      'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first',
      'well', 'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day',
      'most', 'us', 'is', 'was', 'are', 'been', 'has', 'had', 'were', 'said',
      'each', 'which', 'their', 'time', 'will', 'about', 'if', 'up', 'out', 'many'
    ]);
    
    const words = this.extractWords(text);
    const difficultWords = words
      .filter(wordObj => !commonWords.has(wordObj.text.toLowerCase()))
      .filter(wordObj => wordObj.text.length > 3)
      .slice(0, 20); // 限制返回数量
    
    return difficultWords;
  }
}

module.exports = TextProcessService;