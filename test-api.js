/**
 * API测试脚本
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testHealthCheck() {
  console.log('🏥 测试健康检查端点...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return false;
  }
}

async function testWordLookup() {
  console.log('\n📚 测试单词查询API...');
  try {
    const response = await axios.post(`${BASE_URL}/api/ai/word-lookup`, {
      word: 'hello'
    });
    console.log('✅ 单词查询成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 单词查询失败:', error.response?.data || error.message);
    return false;
  }
}

async function testTranslation() {
  console.log('\n🌐 测试翻译API...');
  try {
    const response = await axios.post(`${BASE_URL}/api/ai/translate`, {
      text: 'Hello, how are you?',
      targetLanguage: 'zh'
    });
    console.log('✅ 翻译成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 翻译失败:', error.response?.data || error.message);
    return false;
  }
}

async function testSentenceAnalysis() {
  console.log('\n📝 测试句子分析API...');
  try {
    const response = await axios.post(`${BASE_URL}/api/ai/analyze-sentence`, {
      sentence: 'The quick brown fox jumps over the lazy dog.'
    });
    console.log('✅ 句子分析成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 句子分析失败:', error.response?.data || error.message);
    return false;
  }
}

async function testRateLimiting() {
  console.log('\n🚦 测试限流功能...');
  try {
    const promises = [];
    // 发送多个并发请求测试限流
    for (let i = 0; i < 5; i++) {
      promises.push(
        axios.post(`${BASE_URL}/api/ai/word-lookup`, { word: `test${i}` })
      );
    }
    
    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(`✅ 限流测试完成: ${successful} 成功, ${failed} 失败`);
    return true;
  } catch (error) {
    console.error('❌ 限流测试失败:', error.message);
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🚨 测试错误处理...');
  try {
    // 测试无效的API端点
    await axios.get(`${BASE_URL}/api/invalid-endpoint`);
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ 404错误处理正常:', error.response.data);
      return true;
    } else {
      console.error('❌ 错误处理异常:', error.message);
      return false;
    }
  }
}

async function runAllTests() {
  console.log('🧪 开始API测试...\n');
  
  const tests = [
    { name: '健康检查', fn: testHealthCheck },
    { name: '单词查询', fn: testWordLookup },
    { name: '翻译功能', fn: testTranslation },
    { name: '句子分析', fn: testSentenceAnalysis },
    { name: '限流功能', fn: testRateLimiting },
    { name: '错误处理', fn: testErrorHandling }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      console.error(`❌ ${test.name} 测试异常:`, error.message);
      results.push({ name: test.name, success: false });
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    console.log(`${result.name}: ${status}`);
  });
  
  const passedTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log('='.repeat(50));
  console.log(`总计: ${passedTests}/${totalTests} 测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试都通过了！');
  } else {
    console.log('⚠️  部分测试失败，请检查相关功能');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testHealthCheck,
  testWordLookup,
  testTranslation,
  testSentenceAnalysis,
  testRateLimiting,
  testErrorHandling,
  runAllTests
};
