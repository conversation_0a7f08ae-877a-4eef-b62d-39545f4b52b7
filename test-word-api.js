const axios = require('axios');

async function testWordAPI() {
    const baseURL = 'http://localhost:3001';
    
    console.log('🧪 测试单词详情API...');
    
    try {
        // 测试单词详情API
        console.log('正在测试单词: hello');
        const response = await axios.get(`${baseURL}/api/ai/word-details/hello`, {
            timeout: 30000
        });
        
        console.log('✅ API响应成功!');
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
            console.log('✅ 单词详情获取成功');
            console.log('数据源:', response.data.source);
        } else {
            console.log('❌ API返回失败状态');
        }
        
    } catch (error) {
        console.error('❌ API测试失败:');
        console.error('错误类型:', error.constructor.name);
        console.error('错误消息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        } else if (error.request) {
            console.error('请求发出但没有收到响应');
        } else {
            console.error('请求配置错误');
        }
    }
}

// 运行测试
testWordAPI();
