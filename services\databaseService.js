/**
 * 数据库服务
 * 提供数据存储、查询、更新等功能
 */

const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

class DatabaseService {
  constructor() {
    // 使用环境变量指定数据目录，避免C盘存储
    this.dataDir = process.env.DATA_DIR || path.join(process.cwd(), 'data');
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'english_learning',
      charset: 'utf8mb4',
      timezone: '+08:00'
    };
    this.pool = null;
    this.ensureDataDirectory();
    this.initializeDatabase();
  }

  /**
   * 确保数据目录存在
   */
  async ensureDataDirectory() {
    try {
      await fs.access(this.dataDir);
      logger.info(`Data directory exists: ${this.dataDir}`);
    } catch (error) {
      await fs.mkdir(this.dataDir, { recursive: true });
      logger.info(`Created data directory: ${this.dataDir}`);
    }
  }

  /**
   * 初始化数据库连接
   */
  async initializeDatabase() {
    try {
      // 检查是否配置了数据库
      if (!this.dbConfig.host || this.dbConfig.host === 'localhost') {
        logger.info('数据库未配置或使用默认配置，跳过数据库连接');
        logger.info('系统将使用文件存储模式运行');
        return;
      }

      this.pool = mysql.createPool({
        ...this.dbConfig,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        acquireTimeout: 5000, // 5秒超时
        timeout: 5000
      });

      // 测试连接（设置超时）
      const connection = await Promise.race([
        this.pool.getConnection(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 5000)
        )
      ]);

      await connection.ping();
      connection.release();

      logger.info('✅ 数据库连接成功');

      // 创建必要的表
      await this.createTables();

    } catch (error) {
      logger.warn('⚠️ 数据库连接失败，使用文件存储模式:', error.message);
      this.pool = null; // 确保pool为null
      // 不抛出错误，让应用继续运行
    }
  }

  /**
   * 创建数据库表
   */
  async createTables() {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE
      )`,

      // 学习记录表
      `CREATE TABLE IF NOT EXISTS learning_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        word VARCHAR(100) NOT NULL,
        definition TEXT,
        pronunciation VARCHAR(200),
        example_sentence TEXT,
        learned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        review_count INT DEFAULT 0,
        mastery_level ENUM('new', 'learning', 'familiar', 'mastered') DEFAULT 'new',
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_word (user_id, word),
        INDEX idx_learned_at (learned_at)
      )`,

      // 学习进度表
      `CREATE TABLE IF NOT EXISTS learning_progress (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        total_words INT DEFAULT 0,
        mastered_words INT DEFAULT 0,
        learning_streak INT DEFAULT 0,
        last_study_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_progress (user_id)
      )`
    ];

    for (const sql of tables) {
      try {
        await this.pool.execute(sql);
      } catch (error) {
        logger.error('Failed to create table:', error);
      }
    }

    logger.info('Database tables created/verified successfully');
  }

  /**
   * 测试数据库连接
   */
  async testConnection() {
    if (!this.pool) {
      throw new Error('Database not initialized');
    }

    const connection = await this.pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  }

  /**
   * 保存文章数据
   * @param {Object} articleData - 文章数据
   * @returns {Promise<string>} 文章ID
   */
  async saveArticle(articleData) {
    const articleId = this.generateId();
    const timestamp = new Date().toISOString();
    
    const article = {
      id: articleId,
      title: articleData.title || 'Untitled',
      content: articleData.content,
      source: articleData.source || 'manual',
      createdAt: timestamp,
      updatedAt: timestamp,
      metadata: {
        wordCount: articleData.wordCount || 0,
        sentenceCount: articleData.sentenceCount || 0,
        difficulty: articleData.difficulty || 'unknown'
      }
    };
    
    const filePath = path.join(this.dataDir, `article_${articleId}.json`);
    await fs.writeFile(filePath, JSON.stringify(article, null, 2));
    
    // 更新文章索引
    await this.updateArticleIndex(article);
    
    return articleId;
  }

  /**
   * 获取文章数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Object|null>} 文章数据
   */
  async getArticle(articleId) {
    try {
      const filePath = path.join(this.dataDir, `article_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取所有文章列表
   * @returns {Promise<Array>} 文章列表
   */
  async getAllArticles() {
    try {
      const indexPath = path.join(this.dataDir, 'articles_index.json');
      const data = await fs.readFile(indexPath, 'utf8');
      const index = JSON.parse(data);
      return index.articles || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存单词数据
   * @param {string} articleId - 文章ID
   * @param {Array} words - 单词数组
   * @returns {Promise<void>}
   */
  async saveWords(articleId, words) {
    const filePath = path.join(this.dataDir, `words_${articleId}.json`);
    const wordsData = {
      articleId,
      words,
      createdAt: new Date().toISOString(),
      totalCount: words.length
    };
    
    await fs.writeFile(filePath, JSON.stringify(wordsData, null, 2));
  }

  /**
   * 获取单词数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Array>} 单词数组
   */
  async getWords(articleId) {
    try {
      const filePath = path.join(this.dataDir, `words_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const wordsData = JSON.parse(data);
      return wordsData.words || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存句子数据
   * @param {string} articleId - 文章ID
   * @param {Array} sentences - 句子数组
   * @returns {Promise<void>}
   */
  async saveSentences(articleId, sentences) {
    const filePath = path.join(this.dataDir, `sentences_${articleId}.json`);
    const sentencesData = {
      articleId,
      sentences,
      createdAt: new Date().toISOString(),
      totalCount: sentences.length
    };
    
    await fs.writeFile(filePath, JSON.stringify(sentencesData, null, 2));
  }

  /**
   * 获取句子数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Array>} 句子数组
   */
  async getSentences(articleId) {
    try {
      const filePath = path.join(this.dataDir, `sentences_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const sentencesData = JSON.parse(data);
      return sentencesData.sentences || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存分析结果
   * @param {string} articleId - 文章ID
   * @param {Object} analysis - 分析结果
   * @returns {Promise<void>}
   */
  async saveAnalysis(articleId, analysis) {
    const filePath = path.join(this.dataDir, `analysis_${articleId}.json`);
    const analysisData = {
      articleId,
      analysis,
      createdAt: new Date().toISOString()
    };
    
    await fs.writeFile(filePath, JSON.stringify(analysisData, null, 2));
  }

  /**
   * 获取分析结果
   * @param {string} articleId - 文章ID
   * @returns {Promise<Object|null>} 分析结果
   */
  async getAnalysis(articleId) {
    try {
      const filePath = path.join(this.dataDir, `analysis_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const analysisData = JSON.parse(data);
      return analysisData.analysis || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 删除文章及相关数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteArticle(articleId) {
    try {
      const files = [
        `article_${articleId}.json`,
        `words_${articleId}.json`,
        `sentences_${articleId}.json`,
        `analysis_${articleId}.json`
      ];
      
      for (const file of files) {
        const filePath = path.join(this.dataDir, file);
        try {
          await fs.unlink(filePath);
        } catch (error) {
          // 文件不存在，忽略错误
        }
      }
      
      // 从索引中移除
      await this.removeFromArticleIndex(articleId);
      
      return true;
    } catch (error) {
      console.error('删除文章失败:', error);
      return false;
    }
  }

  /**
   * 更新文章索引
   * @param {Object} article - 文章数据
   * @returns {Promise<void>}
   */
  async updateArticleIndex(article) {
    const indexPath = path.join(this.dataDir, 'articles_index.json');
    
    let index = { articles: [] };
    try {
      const data = await fs.readFile(indexPath, 'utf8');
      index = JSON.parse(data);
    } catch (error) {
      // 索引文件不存在，使用默认值
    }
    
    // 移除已存在的同ID文章
    index.articles = index.articles.filter(a => a.id !== article.id);
    
    // 添加新文章到索引
    index.articles.unshift({
      id: article.id,
      title: article.title,
      createdAt: article.createdAt,
      updatedAt: article.updatedAt,
      wordCount: article.metadata.wordCount,
      difficulty: article.metadata.difficulty
    });
    
    // 限制索引大小，只保留最新的100篇文章
    if (index.articles.length > 100) {
      index.articles = index.articles.slice(0, 100);
    }
    
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
  }

  /**
   * 从文章索引中移除
   * @param {string} articleId - 文章ID
   * @returns {Promise<void>}
   */
  async removeFromArticleIndex(articleId) {
    const indexPath = path.join(this.dataDir, 'articles_index.json');
    
    try {
      const data = await fs.readFile(indexPath, 'utf8');
      const index = JSON.parse(data);
      
      index.articles = index.articles.filter(a => a.id !== articleId);
      
      await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
    } catch (error) {
      // 索引文件不存在或其他错误，忽略
    }
  }

  /**
   * 搜索文章
   * @param {string} query - 搜索关键词
   * @returns {Promise<Array>} 搜索结果
   */
  async searchArticles(query) {
    const articles = await this.getAllArticles();
    
    if (!query) return articles;
    
    const searchTerm = query.toLowerCase();
    const results = [];
    
    for (const articleInfo of articles) {
      const article = await this.getArticle(articleInfo.id);
      if (article) {
        const titleMatch = article.title.toLowerCase().includes(searchTerm);
        const contentMatch = article.content.toLowerCase().includes(searchTerm);
        
        if (titleMatch || contentMatch) {
          results.push({
            ...articleInfo,
            relevance: titleMatch ? 2 : 1 // 标题匹配权重更高
          });
        }
      }
    }
    
    // 按相关性排序
    return results.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStatistics() {
    const articles = await this.getAllArticles();
    
    const stats = {
      totalArticles: articles.length,
      totalWords: 0,
      difficultyDistribution: {
        beginner: 0,
        intermediate: 0,
        advanced: 0,
        unknown: 0
      },
      recentActivity: articles.slice(0, 5)
    };
    
    articles.forEach(article => {
      stats.totalWords += article.wordCount || 0;
      const difficulty = article.difficulty || 'unknown';
      if (stats.difficultyDistribution[difficulty] !== undefined) {
        stats.difficultyDistribution[difficulty]++;
      }
    });
    
    return stats;
  }
}

module.exports = DatabaseService;