{"timestamp":"2025-08-05T15:44:28.929Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
{"timestamp":"2025-08-05T15:45:41.011Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
{"timestamp":"2025-08-05T15:51:37.680Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
{"timestamp":"2025-08-05T15:52:54.593Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
{"timestamp":"2025-08-05T15:52:55.435Z","level":"error","message":"GET /health 503","method":"GET","url":"/health","statusCode":503,"responseTime":"853ms","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22000.2538","contentLength":"336"}
{"timestamp":"2025-08-05T15:54:06.348Z","level":"error","message":"Error 400: Bad escaped character in JSON at position 8 (line 1 column 9)","url":"/api/ai/word-lookup","method":"POST","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22000.2538","stack":"SyntaxError: Bad escaped character in JSON at position 8 (line 1 column 9)\n    at JSON.parse (<anonymous>)\n    at parse (H:\\project\\hwzfuke\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at H:\\project\\hwzfuke\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (H:\\project\\hwzfuke\\node_modules\\raw-body\\index.js:238:16)\n    at done (H:\\project\\hwzfuke\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (H:\\project\\hwzfuke\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}
{"timestamp":"2025-08-05T15:55:42.737Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
{"timestamp":"2025-08-05T15:55:42.741Z","level":"error","message":"GET /health 503","method":"GET","url":"/health","statusCode":503,"responseTime":"15ms","ip":"::ffff:127.0.0.1","userAgent":"axios/1.11.0","contentLength":"337"}
{"timestamp":"2025-08-05T15:55:44.377Z","level":"error","message":"Error 404: Can't find /api/ai/analyze-sentence on this server!","url":"/api/ai/analyze-sentence","method":"POST","ip":"::ffff:127.0.0.1","userAgent":"axios/1.11.0","stack":"Error: Can't find /api/ai/analyze-sentence on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:265:14)"}
{"timestamp":"2025-08-05T15:55:44.421Z","level":"error","message":"POST /api/ai/analyze-sentence 404","method":"POST","url":"/api/ai/analyze-sentence","statusCode":404,"responseTime":"19ms","ip":"::ffff:127.0.0.1","userAgent":"axios/1.11.0","contentLength":"1176"}
{"timestamp":"2025-08-05T15:55:45.022Z","level":"error","message":"Error 404: Can't find /api/invalid-endpoint on this server!","url":"/api/invalid-endpoint","method":"GET","ip":"::ffff:127.0.0.1","userAgent":"axios/1.11.0","stack":"Error: Can't find /api/invalid-endpoint on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-05T15:55:45.022Z","level":"error","message":"GET /api/invalid-endpoint 404","method":"GET","url":"/api/invalid-endpoint","statusCode":404,"responseTime":"2ms","ip":"::ffff:127.0.0.1","userAgent":"axios/1.11.0","contentLength":"1136"}
{"timestamp":"2025-08-05T15:56:28.222Z","level":"error","message":"Error 404: Can't find /favicon.ico on this server!","url":"/favicon.ico","method":"GET","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","stack":"Error: Can't find /favicon.ico on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-05T15:56:28.222Z","level":"error","message":"GET /favicon.ico 404","method":"GET","url":"/favicon.ico","statusCode":404,"responseTime":"21ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","contentLength":"1118"}
{"timestamp":"2025-08-05T15:56:28.230Z","level":"error","message":"Error 404: Can't find /sw.js on this server!","url":"/sw.js","method":"GET","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","stack":"Error: Can't find /sw.js on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-05T15:56:28.235Z","level":"error","message":"GET /sw.js 404","method":"GET","url":"/sw.js","statusCode":404,"responseTime":"3ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","contentLength":"1106"}
{"timestamp":"2025-08-05T16:38:00.586Z","level":"error","message":"Database initialization failed:","code":"ECONNREFUSED","fatal":true}
