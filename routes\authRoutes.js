/**
 * 用户认证路由
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const { authService, authenticate, optionalAuth } = require('../middleware/auth');
const { AppError, asyncHandler } = require('../middleware/errorHandler');
const { loginLimiter, registerLimiter } = require('../middleware/rateLimiter');
const logger = require('../utils/logger');

const router = express.Router();

// 输入验证规则
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// 验证输入的中间件
const validateInput = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    throw new AppError(`Validation failed: ${errorMessages.join(', ')}`, 400, 'VALIDATION_ERROR');
  }
  next();
};

/**
 * 用户注册
 * POST /api/auth/register
 */
router.post('/register', 
  registerLimiter,
  registerValidation,
  validateInput,
  asyncHandler(async (req, res) => {
    const { username, email, password } = req.body;
    
    logger.info(`Registration attempt for email: ${email}`);
    
    const result = await authService.register(username, email, password);
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: result
    });
  })
);

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login',
  loginLimiter,
  loginValidation,
  validateInput,
  asyncHandler(async (req, res) => {
    const { email, password } = req.body;
    
    logger.info(`Login attempt for email: ${email}`);
    
    const result = await authService.login(email, password);
    
    res.json({
      success: true,
      message: 'Login successful',
      data: result
    });
  })
);

/**
 * 获取当前用户信息
 * GET /api/auth/me
 */
router.get('/me',
  authenticate,
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      data: {
        user: req.user
      }
    });
  })
);

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout',
  optionalAuth,
  asyncHandler(async (req, res) => {
    // 在实际应用中，这里可以将token加入黑名单
    // 目前只是返回成功响应，客户端需要删除本地token
    
    if (req.user) {
      logger.info(`User logged out: ${req.user.email}`);
    }
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
  })
);

/**
 * 刷新token
 * POST /api/auth/refresh
 */
router.post('/refresh',
  authenticate,
  asyncHandler(async (req, res) => {
    // 生成新的token
    const newToken = authService.generateToken(req.user.id);
    
    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken,
        user: req.user
      }
    });
  })
);

/**
 * 修改密码
 * PUT /api/auth/password
 */
router.put('/password',
  authenticate,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('New password must be at least 6 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
  ],
  validateInput,
  asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    
    // 验证当前密码
    const user = await authService.getCurrentUser(req.user.id);
    const isValidPassword = await authService.verifyPassword(currentPassword, user.password_hash);
    
    if (!isValidPassword) {
      throw new AppError('Current password is incorrect', 400, 'INVALID_PASSWORD');
    }
    
    // 更新密码
    const hashedPassword = await authService.hashPassword(newPassword);
    await authService.updateUserPassword(req.user.id, hashedPassword);
    
    logger.info(`Password changed for user: ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'Password updated successfully'
    });
  })
);

/**
 * 删除账户
 * DELETE /api/auth/account
 */
router.delete('/account',
  authenticate,
  [
    body('password')
      .notEmpty()
      .withMessage('Password is required to delete account')
  ],
  validateInput,
  asyncHandler(async (req, res) => {
    const { password } = req.body;
    
    // 验证密码
    const user = await authService.getCurrentUser(req.user.id);
    const isValidPassword = await authService.verifyPassword(password, user.password_hash);
    
    if (!isValidPassword) {
      throw new AppError('Password is incorrect', 400, 'INVALID_PASSWORD');
    }
    
    // 删除用户账户
    await authService.deleteUser(req.user.id);
    
    logger.info(`Account deleted for user: ${req.user.email}`);
    
    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  })
);

module.exports = router;
