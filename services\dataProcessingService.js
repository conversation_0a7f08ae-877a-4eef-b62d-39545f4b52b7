/**
 * 数据处理服务
 * 提供数据清理、转换、验证和格式化功能
 */

const crypto = require('crypto');
const { URL } = require('url');

class DataProcessingService {
  constructor() {
    this.stopWords = new Set([
      // 中文停用词
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
      // 英文停用词
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ]);
  }

  /**
   * 处理抓取的文章数据
   */
  processArticleData(articleData, options = {}) {
    if (!articleData.success) {
      return articleData;
    }

    try {
      const processed = {
        ...articleData,
        id: this.generateArticleId(articleData.url, articleData.title),
        title: this.cleanText(articleData.title),
        author: this.cleanText(articleData.author),
        description: this.cleanText(articleData.description),
        content: this.cleanHtmlContent(articleData.content),
        plainText: this.cleanText(articleData.plainText),
        tags: this.processTags(articleData.tags),
        images: this.processImages(articleData.images, options),
        links: this.processLinks(articleData.links, options),
        keywords: this.extractKeywords(articleData.plainText, options),
        summary: this.generateSummary(articleData.plainText, options),
        processedAt: new Date().toISOString(),
        qualityScore: this.calculateQualityScore(articleData)
      };

      const validation = this.validateProcessedData(processed);
      processed.validation = validation;

      return processed;
    } catch (error) {
      return {
        ...articleData,
        processingError: error.message,
        processedAt: new Date().toISOString()
      };
    }
  }

  /**
   * 生成文章唯一ID
   */
  generateArticleId(url, title) {
    const content = `${url}|${title}|${Date.now()}`;
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * 清理文本内容
   */
  cleanText(text) {
    if (!text || typeof text !== 'string') return '';
    
    return text
      .replace(/\s+/g, ' ')
      .trim()
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      .replace(/[\u200B-\u200D\uFEFF]/g, '');