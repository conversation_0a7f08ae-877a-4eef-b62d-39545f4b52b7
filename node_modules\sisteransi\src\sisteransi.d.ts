export const beep: string;
export const clear: string;

export namespace cursor {
    export const left: string;
    export const hide: string;
    export const show: string;
    export const save: string;
    export const restore: string;

    export function to(x: number, y?: number): string;
    export function move(x: number, y: number): string;
    export function up(count?: number): string;
    export function down(count?: number): string;
    export function forward(count?: number): string;
    export function backward(count?: number): string;
    export function nextLine(count?: number): string;
    export function prevLine(count?: number): string;
}

export namespace scroll {
    export function up(count?: number): string;
    export function down(count?: number): string;
}

export namespace erase {
    export const screen: string;
    export const line: string;
    export const lineEnd: string;
    export const lineStart: string;

    export function up(count?: number): string;
    export function down(count?: number): string;
    export function lines(count: number): string;
}
