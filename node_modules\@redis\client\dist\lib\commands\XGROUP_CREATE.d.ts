import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 2;
interface XGroupCreateOptions {
    MKSTREAM?: true;
}
export declare function transformArguments(key: RedisCommandArgument, group: RedisCommandArgument, id: RedisCommandArgument, options?: XGroupCreateOptions): RedisCommandArguments;
export declare function transformReply(): RedisCommandArgument;
export {};
