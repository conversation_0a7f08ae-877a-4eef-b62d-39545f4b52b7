<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .word {
            color: #007bff;
            cursor: pointer;
            text-decoration: underline;
        }
        .word:hover {
            background-color: #f0f8ff;
        }
    </style>
</head>
<body>
    <h1>修复测试页面</h1>
    
    <div class="test-section">
        <h2>测试1：悬停弹窗消失问题</h2>
        <p>请将鼠标悬停在以下单词上，然后快速移开，检查弹窗是否正确消失：</p>
        <p>AI technology has brought unprecedented innovation to <span class="word">various</span> fields.</p>
        <p>It has transformed healthcare, automating routine tasks and enhancing diagnostic <span class="word">accuracy</span>.</p>
    </div>
    
    <div class="test-section">
        <h2>测试2：翻译内容显示和后台重试</h2>
        <p>悬停以下单词，应该能看到实际翻译内容（即使质量较低），同时系统会在后台重试获取更好的翻译：</p>
        <p>The <span class="word">innovation</span> in artificial intelligence continues to <span class="word">accelerate</span>.</p>
        <p>Machine learning algorithms can <span class="word">process</span> vast amounts of data efficiently.</p>
        <p><strong>预期行为：</strong></p>
        <ul>
            <li>立即显示翻译内容（不显示"翻译质量较低，请稍后重试"）</li>
            <li>如果翻译质量较低，系统会在后台重试</li>
            <li>获得更好翻译时会显示改进通知</li>
            <li>控制台会显示重试过程日志</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>使用说明</h2>
        <ol>
            <li>打开浏览器开发者工具的控制台</li>
            <li>悬停在蓝色单词上</li>
            <li>观察弹窗行为和控制台日志</li>
            <li>检查：
                <ul>
                    <li>鼠标移开后弹窗是否立即消失</li>
                    <li>控制台是否显示质量检查相关日志</li>
                    <li>低质量翻译是否被正确过滤</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        // 模拟单词悬停功能（简化版）
        document.querySelectorAll('.word').forEach(wordElement => {
            wordElement.addEventListener('mouseenter', (e) => {
                console.log(`悬停在单词: ${e.target.textContent}`);
                // 这里会调用实际的showWordTooltip函数
            });
            
            wordElement.addEventListener('mouseleave', (e) => {
                console.log(`离开单词: ${e.target.textContent}`);
                // 这里会调用实际的hideWordTooltip函数
            });
        });
    </script>
</body>
</html>
