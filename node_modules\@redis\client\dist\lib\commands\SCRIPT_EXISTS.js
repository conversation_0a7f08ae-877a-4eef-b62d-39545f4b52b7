"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformReply = exports.transformArguments = void 0;
const generic_transformers_1 = require("./generic-transformers");
function transformArguments(sha1) {
    return (0, generic_transformers_1.pushVerdictArguments)(['SCRIPT', 'EXISTS'], sha1);
}
exports.transformArguments = transformArguments;
var generic_transformers_2 = require("./generic-transformers");
Object.defineProperty(exports, "transformReply", { enumerable: true, get: function () { return generic_transformers_2.transformBooleanArrayReply; } });
