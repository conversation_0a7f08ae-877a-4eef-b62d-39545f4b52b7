/**
 * 日志系统
 * 提供结构化日志记录功能
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = process.env.LOG_DIR || path.join(process.cwd(), 'logs');
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.maxFileSize = this.parseSize(process.env.LOG_MAX_SIZE || '10m');
    this.maxFiles = parseInt(process.env.LOG_MAX_FILES || '5');
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.colors = {
      error: '\x1b[31m',
      warn: '\x1b[33m',
      info: '\x1b[36m',
      debug: '\x1b[37m',
      reset: '\x1b[0m'
    };
    
    this.ensureLogDirectory();
  }

  // 确保日志目录存在
  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  // 解析文件大小
  parseSize(size) {
    const units = { b: 1, k: 1024, m: 1024 * 1024, g: 1024 * 1024 * 1024 };
    const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
    if (!match) return 10 * 1024 * 1024; // 默认10MB
    return parseInt(match[1]) * (units[match[2]] || 1);
  }

  // 格式化日志消息
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };
    return JSON.stringify(logEntry);
  }

  // 获取日志文件路径
  getLogFilePath(level) {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${level}-${date}.log`);
  }

  // 检查文件大小并轮转
  async rotateLogFile(filePath) {
    try {
      const stats = fs.statSync(filePath);
      if (stats.size > this.maxFileSize) {
        const dir = path.dirname(filePath);
        const ext = path.extname(filePath);
        const basename = path.basename(filePath, ext);
        
        // 轮转现有文件
        for (let i = this.maxFiles - 1; i > 0; i--) {
          const oldFile = path.join(dir, `${basename}.${i}${ext}`);
          const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
          
          if (fs.existsSync(oldFile)) {
            if (i === this.maxFiles - 1) {
              fs.unlinkSync(oldFile); // 删除最老的文件
            } else {
              fs.renameSync(oldFile, newFile);
            }
          }
        }
        
        // 重命名当前文件
        const rotatedFile = path.join(dir, `${basename}.1${ext}`);
        fs.renameSync(filePath, rotatedFile);
      }
    } catch (error) {
      console.error('Log rotation failed:', error);
    }
  }

  // 写入日志文件
  async writeToFile(level, message, meta) {
    try {
      const filePath = this.getLogFilePath(level);
      await this.rotateLogFile(filePath);
      
      const logMessage = this.formatMessage(level, message, meta);
      fs.appendFileSync(filePath, logMessage + '\n');
    } catch (error) {
      console.error('Failed to write log:', error);
    }
  }

  // 控制台输出
  writeToConsole(level, message, meta) {
    const timestamp = new Date().toISOString();
    const color = this.colors[level] || this.colors.reset;
    const reset = this.colors.reset;
    
    let output = `${color}[${timestamp}] ${level.toUpperCase()}: ${message}${reset}`;
    
    if (Object.keys(meta).length > 0) {
      output += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    if (level === 'error') {
      console.error(output);
    } else {
      console.log(output);
    }
  }

  // 检查日志级别
  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel];
  }

  // 通用日志方法
  log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;
    
    // 控制台输出
    this.writeToConsole(level, message, meta);
    
    // 文件输出
    this.writeToFile(level, message, meta);
  }

  // 具体日志方法
  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  // HTTP请求日志
  httpLog(req, res, responseTime) {
    const meta = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentLength: res.get('Content-Length')
    };

    const level = res.statusCode >= 400 ? 'error' : 'info';
    this.log(level, `${req.method} ${req.originalUrl} ${res.statusCode}`, meta);
  }

  // API调用日志
  apiLog(service, action, success, responseTime, meta = {}) {
    const logMeta = {
      service,
      action,
      success,
      responseTime: `${responseTime}ms`,
      ...meta
    };

    const level = success ? 'info' : 'error';
    const message = `API ${service}.${action} ${success ? 'success' : 'failed'}`;
    this.log(level, message, logMeta);
  }
}

// 创建单例实例
const logger = new Logger();

module.exports = logger;
