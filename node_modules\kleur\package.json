{"name": "kleur", "version": "3.0.3", "repository": "lukeed/kleur", "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js", "*.d.ts"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}}