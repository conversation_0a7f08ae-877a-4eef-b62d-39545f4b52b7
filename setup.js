/**
 * 项目安装和配置脚本
 * 避免在C盘创建文件，配置环境变量
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class ProjectSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.config = {
      dataDir: '',
      logDir: '',
      uploadDir: '',
      zhipuApiKey: '',
      dbConfig: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: '',
        database: 'english_learning'
      }
    };
  }

  // 询问用户输入
  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  // 检查目录是否在C盘
  isOnCDrive(dirPath) {
    const resolved = path.resolve(dirPath);
    return resolved.toLowerCase().startsWith('c:');
  }

  // 获取推荐的数据目录
  getRecommendedDataDir() {
    const drives = ['D:', 'E:', 'F:'];
    for (const drive of drives) {
      try {
        if (fs.existsSync(drive)) {
          return path.join(drive, 'english-learning-data');
        }
      } catch (error) {
        // 忽略错误，继续检查下一个驱动器
      }
    }
    // 如果没有其他驱动器，使用项目目录
    return path.join(process.cwd(), 'data');
  }

  // 配置数据目录
  async configureDataDirectories() {
    console.log('\n📁 配置数据存储目录');
    console.log('为了避免在C盘存储数据，请配置以下目录：\n');

    // 数据目录
    const recommendedDataDir = this.getRecommendedDataDir();
    const dataDir = await this.question(
      `数据目录 (推荐: ${recommendedDataDir}): `
    ) || recommendedDataDir;

    if (this.isOnCDrive(dataDir)) {
      console.log('⚠️  警告：您选择的目录在C盘，建议使用其他驱动器');
      const confirm = await this.question('是否继续使用此目录？(y/N): ');
      if (confirm.toLowerCase() !== 'y') {
        return await this.configureDataDirectories();
      }
    }

    this.config.dataDir = path.resolve(dataDir);

    // 日志目录
    const logDir = await this.question(
      `日志目录 (默认: ${path.join(this.config.dataDir, 'logs')}): `
    ) || path.join(this.config.dataDir, 'logs');
    this.config.logDir = path.resolve(logDir);

    // 上传目录
    const uploadDir = await this.question(
      `上传目录 (默认: ${path.join(this.config.dataDir, 'uploads')}): `
    ) || path.join(this.config.dataDir, 'uploads');
    this.config.uploadDir = path.resolve(uploadDir);

    console.log('\n✅ 目录配置完成：');
    console.log(`   数据目录: ${this.config.dataDir}`);
    console.log(`   日志目录: ${this.config.logDir}`);
    console.log(`   上传目录: ${this.config.uploadDir}`);
  }

  // 配置API密钥
  async configureApiKeys() {
    console.log('\n🔑 配置API密钥');
    
    this.config.zhipuApiKey = await this.question(
      '请输入智谱AI API密钥: '
    );

    if (!this.config.zhipuApiKey) {
      console.log('⚠️  警告：未配置API密钥，某些功能可能无法使用');
    }
  }

  // 配置数据库
  async configureDatabase() {
    console.log('\n🗄️  配置数据库连接');
    
    const useDatabase = await this.question(
      '是否使用MySQL数据库？(Y/n): '
    );

    if (useDatabase.toLowerCase() !== 'n') {
      this.config.dbConfig.host = await this.question(
        `数据库主机 (默认: ${this.config.dbConfig.host}): `
      ) || this.config.dbConfig.host;

      this.config.dbConfig.port = parseInt(await this.question(
        `数据库端口 (默认: ${this.config.dbConfig.port}): `
      )) || this.config.dbConfig.port;

      this.config.dbConfig.user = await this.question(
        `数据库用户名 (默认: ${this.config.dbConfig.user}): `
      ) || this.config.dbConfig.user;

      this.config.dbConfig.password = await this.question(
        '数据库密码: '
      );

      this.config.dbConfig.database = await this.question(
        `数据库名称 (默认: ${this.config.dbConfig.database}): `
      ) || this.config.dbConfig.database;
    }
  }

  // 创建目录
  async createDirectories() {
    console.log('\n📂 创建必要目录...');
    
    const directories = [
      this.config.dataDir,
      this.config.logDir,
      this.config.uploadDir
    ];

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`✅ 创建目录: ${dir}`);
        } else {
          console.log(`📁 目录已存在: ${dir}`);
        }
      } catch (error) {
        console.error(`❌ 创建目录失败: ${dir}`, error.message);
      }
    }
  }

  // 生成环境配置文件
  async generateEnvFile() {
    console.log('\n📝 生成环境配置文件...');
    
    const envContent = `# 英语学习系统环境配置
# 由setup.js自动生成于 ${new Date().toISOString()}

# 服务器配置
PORT=3000
NODE_ENV=development

# API密钥配置
ZHIPU_API_KEY=${this.config.zhipuApiKey}

# 数据库配置
DB_HOST=${this.config.dbConfig.host}
DB_PORT=${this.config.dbConfig.port}
DB_NAME=${this.config.dbConfig.database}
DB_USER=${this.config.dbConfig.user}
DB_PASSWORD=${this.config.dbConfig.password}

# 文件存储配置 (避免C盘存储)
DATA_DIR=${this.config.dataDir.replace(/\\/g, '/')}
LOG_DIR=${this.config.logDir.replace(/\\/g, '/')}
UPLOAD_DIR=${this.config.uploadDir.replace(/\\/g, '/')}

# 安全配置
JWT_SECRET=${this.generateRandomString(64)}
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 日志配置
LOG_LEVEL=info
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 限流配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=txt,pdf,doc,docx
`;

    try {
      fs.writeFileSync('.env', envContent);
      console.log('✅ 环境配置文件 .env 已生成');
    } catch (error) {
      console.error('❌ 生成环境配置文件失败:', error.message);
    }
  }

  // 生成随机字符串
  generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 显示完成信息
  showCompletionInfo() {
    console.log('\n🎉 项目配置完成！');
    console.log('\n📋 下一步操作：');
    console.log('1. 安装依赖: npm install');
    console.log('2. 启动开发服务器: npm run dev');
    console.log('3. 访问应用: http://localhost:3000');
    console.log('\n📁 数据存储位置：');
    console.log(`   数据: ${this.config.dataDir}`);
    console.log(`   日志: ${this.config.logDir}`);
    console.log(`   上传: ${this.config.uploadDir}`);
    console.log('\n⚠️  重要提示：');
    console.log('- 请妥善保管 .env 文件中的密钥信息');
    console.log('- 不要将 .env 文件提交到版本控制系统');
    console.log('- 如需修改配置，可直接编辑 .env 文件');
  }

  // 运行安装程序
  async run() {
    console.log('🚀 英语学习系统安装向导');
    console.log('=====================================\n');

    try {
      await this.configureDataDirectories();
      await this.configureApiKeys();
      await this.configureDatabase();
      await this.createDirectories();
      await this.generateEnvFile();
      
      this.showCompletionInfo();
    } catch (error) {
      console.error('❌ 安装过程中出现错误:', error.message);
    } finally {
      this.rl.close();
    }
  }
}

// 运行安装程序
if (require.main === module) {
  const setup = new ProjectSetup();
  setup.run();
}

module.exports = ProjectSetup;
