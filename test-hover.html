<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬停翻译测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-text {
            font-size: 18px;
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .word {
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.3s ease;
            position: relative;
        }
        .word:hover {
            background: #fff3cd;
            color: #856404;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>悬停翻译功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <p>将鼠标悬停在下面的英文单词上，应该会：</p>
        <ul>
            <li>自动播放单词发音</li>
            <li>显示翻译提示框</li>
            <li>提示框包含音标、词性、中文释义</li>
        </ul>
    </div>
    
    <div class="test-text">
        <p>The <span class="word" data-word="unprecedented">unprecedented</span> growth of <span class="word" data-word="technology">technology</span> has brought <span class="word" data-word="innovation">innovation</span> to various industries.</p>
        
        <p>This <span class="word" data-word="development">development</span> represents a <span class="word" data-word="significant">significant</span> <span class="word" data-word="breakthrough">breakthrough</span> in modern <span class="word" data-word="science">science</span>.</p>
    </div>
    
    <script>
        // 简化的测试脚本
        document.querySelectorAll('.word').forEach(wordSpan => {
            const word = wordSpan.dataset.word;
            
            wordSpan.addEventListener('mouseenter', (e) => {
                console.log(`悬停在单词: ${word}`);
                // 这里应该调用悬停函数
                if (typeof showWordTooltip === 'function') {
                    showWordTooltip(e, word);
                } else {
                    console.log('showWordTooltip 函数未找到');
                }
            });
            
            wordSpan.addEventListener('mouseleave', () => {
                console.log(`离开单词: ${word}`);
                if (typeof hideWordTooltip === 'function') {
                    hideWordTooltip();
                } else {
                    console.log('hideWordTooltip 函数未找到');
                }
            });
        });
    </script>
</body>
</html>
