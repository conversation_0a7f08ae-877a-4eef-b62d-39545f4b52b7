"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeSeriesBucketTimestamp = exports.TimeSeriesReducers = exports.TimeSeriesAggregationType = exports.TimeSeriesEncoding = exports.TimeSeriesDuplicatePolicies = exports.default = void 0;
var commands_1 = require("./commands");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return commands_1.default; } });
var commands_2 = require("./commands");
Object.defineProperty(exports, "TimeSeriesDuplicatePolicies", { enumerable: true, get: function () { return commands_2.TimeSeriesDuplicatePolicies; } });
Object.defineProperty(exports, "TimeSeriesEncoding", { enumerable: true, get: function () { return commands_2.TimeSeriesEncoding; } });
Object.defineProperty(exports, "TimeSeriesAggregationType", { enumerable: true, get: function () { return commands_2.TimeSeriesAggregationType; } });
Object.defineProperty(exports, "TimeSeriesReducers", { enumerable: true, get: function () { return commands_2.TimeSeriesReducers; } });
Object.defineProperty(exports, "TimeSeriesBucketTimestamp", { enumerable: true, get: function () { return commands_2.TimeSeriesBucketTimestamp; } });
