const axios = require('axios');
require('dotenv').config();

// 智谱AI配置
const ZHIPU_API_KEY = process.env.ZHIPU_API_KEY;
const ZHIPU_BASE_URL = 'https://open.bigmodel.cn/api/paas/v4/';

console.log('智谱AI API密钥:', ZHIPU_API_KEY ? '已配置' : '未配置');

async function testZhipuAPI() {
    if (!ZHIPU_API_KEY) {
        console.error('❌ ZHIPU_API_KEY not found in environment variables');
        return;
    }

    const messages = [
        {
            role: "system",
            content: "你是一个专业的英语词典助手。"
        },
        {
            role: "user",
            content: "请为英语单词'hello'提供基本信息，包括音标、中文释义、词性。请用简短的文字回答。"
        }
    ];

    try {
        console.log('🧪 测试智谱AI API...');
        console.log('API URL:', `${ZHIPU_BASE_URL}chat/completions`);
        
        const requestData = {
            model: 'glm-4-flash',
            messages: messages,
            temperature: 0.7,
            max_tokens: 500,
            stream: false
        };
        
        console.log('发送请求...');
        const response = await axios.post(
            `${ZHIPU_BASE_URL}chat/completions`,
            requestData,
            {
                headers: {
                    'Authorization': `Bearer ${ZHIPU_API_KEY}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout: 30000
            }
        );
        
        console.log('✅ API调用成功!');
        console.log('响应状态:', response.status);
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('❌ API调用失败:');
        console.error('错误类型:', error.constructor.name);
        console.error('错误消息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.error('请求发出但没有收到响应');
        } else {
            console.error('请求配置错误');
        }
    }
}

testZhipuAPI();
