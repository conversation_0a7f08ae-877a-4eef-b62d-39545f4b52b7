# 英语学习助手 - 前端版本

这是一个纯前端的英语学习应用，可以帮助用户通过阅读英文文章来学习英语单词和句子。

## 功能特点

### 📝 多种文本输入方式
- **手动输入**: 直接粘贴或输入英文文本
- **AI搜索**: 根据关键词搜索相关英文文章（模拟功能）
- **文件上传**: 支持上传文本文件进行学习

### 🎯 智能学习功能
- **句子分割**: 自动将文章分割成句子，便于逐句学习
- **单词点击**: 点击任意单词查看详细释义、音标、例句
- **语音朗读**: 支持单词和句子的语音播放
- **中英对照**: 显示文章的中文翻译

### 📊 学习进度跟踪
- **学习统计**: 显示总单词数、已掌握单词数、学习时间等
- **最近学习**: 记录最近学习的单词和掌握状态
- **进度可视化**: 直观的进度条显示学习进度

### 🎨 用户体验
- **响应式设计**: 支持桌面和移动设备
- **现代UI**: 简洁美观的界面设计
- **快捷键支持**: 空格键播放/暂停，ESC键关闭弹窗
- **字体调节**: 可调整文本字体大小

## 使用方法

1. **打开应用**: 在浏览器中打开 `index.html` 文件

2. **输入文本**: 
   - 选择「手动输入」标签，粘贴英文文本
   - 或选择「AI搜索」，输入关键词搜索文章
   - 或选择「文件上传」，上传文本文件

3. **开始学习**:
   - 点击「开始处理」按钮
   - 系统会自动跳转到学习界面

4. **学习操作**:
   - 点击任意单词查看详细信息
   - 点击播放按钮听句子发音
   - 使用「显示翻译」查看中文对照
   - 调整字体大小以获得最佳阅读体验

5. **查看进度**:
   - 切换到「学习进度」标签查看学习统计
   - 查看最近学习的单词列表

## 技术特点

- **纯前端实现**: 无需后端服务器，可直接在浏览器中运行
- **模拟数据**: 使用模拟数据展示功能，便于调试和演示
- **Web Speech API**: 利用浏览器原生语音合成功能
- **现代CSS**: 使用CSS Grid、Flexbox等现代布局技术
- **响应式设计**: 适配各种屏幕尺寸

## 文件结构

```
hwzfuke/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
└── README.md           # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 注意事项

1. **语音功能**: 需要浏览器支持Web Speech API
2. **文件上传**: 仅支持文本文件格式
3. **模拟数据**: 当前版本使用模拟数据，实际部署时需要连接真实的API服务

## 后续开发计划

- [ ] 连接真实的翻译API
- [ ] 连接词典API获取准确的单词信息
- [ ] 添加用户账户系统
- [ ] 实现学习进度的持久化存储
- [ ] 添加更多学习模式（如单词卡片、测试等）
- [ ] 支持更多文件格式（PDF、Word等）

## 开发调试

这个前端版本非常适合用于:
- 界面设计和用户体验测试
- 功能流程验证
- 前端逻辑调试
- 演示和原型展示

所有的数据交互都通过模拟实现，可以完整体验应用的各项功能。