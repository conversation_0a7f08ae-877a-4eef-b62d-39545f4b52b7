{"name": "@types/yargs", "version": "17.0.33", "description": "TypeScript definitions for yargs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>", "url": "https://github.com/poelstra"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana", "url": "https://github.com/mizunashi-mana"}, {"name": "<PERSON><PERSON>", "githubUsername": "pushplay", "url": "https://github.com/pushplay"}, {"name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC", "url": "https://github.com/JimiC"}, {"name": "Steffen Viken Valvåg", "githubUsername": "steffenvv", "url": "https://github.com/steffenvv"}, {"name": "<PERSON>", "githubUsername": "forivall", "url": "https://github.com/forivall"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen"}, {"name": "<PERSON>", "githubUsername": "bcoe", "url": "https://github.com/bcoe"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yargs"}, "scripts": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesPublisherContentHash": "7b837c3dffe16c68f89717a71a88af716d72acc79790394e14f9cafa6be33a2f", "typeScriptVersion": "4.8"}