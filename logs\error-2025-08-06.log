{"timestamp":"2025-08-06T17:15:19.420Z","level":"error","message":"Error 404: Can't find /favicon.ico on this server!","url":"/favicon.ico","method":"GET","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","stack":"Error: Can't find /favicon.ico on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-06T17:15:19.422Z","level":"error","message":"GET /favicon.ico 404","method":"GET","url":"/favicon.ico","statusCode":404,"responseTime":"24ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","contentLength":"1118"}
{"timestamp":"2025-08-06T17:15:23.950Z","level":"error","message":"Error 404: Can't find /sw.js on this server!","url":"/sw.js","method":"GET","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","stack":"Error: Can't find /sw.js on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-06T17:15:23.951Z","level":"error","message":"GET /sw.js 404","method":"GET","url":"/sw.js","statusCode":404,"responseTime":"3ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","contentLength":"1106"}
{"timestamp":"2025-08-06T17:17:05.635Z","level":"error","message":"Error 404: Can't find /.well-known/appspecific/com.chrome.devtools.json on this server!","url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","stack":"Error: Can't find /.well-known/appspecific/com.chrome.devtools.json on this server!\n    at notFoundHandler (H:\\project\\hwzfuke\\middleware\\errorHandler.js:129:15)\n    at Layer.handle [as handle_request] (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (H:\\project\\hwzfuke\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at SendStream.error (H:\\project\\hwzfuke\\node_modules\\serve-static\\index.js:121:7)\n    at SendStream.emit (node:events:518:28)"}
{"timestamp":"2025-08-06T17:17:05.636Z","level":"error","message":"GET /.well-known/appspecific/com.chrome.devtools.json 404","method":"GET","url":"/.well-known/appspecific/com.chrome.devtools.json","statusCode":404,"responseTime":"3ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","contentLength":"1192"}
