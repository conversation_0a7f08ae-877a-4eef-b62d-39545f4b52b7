# 英语背单词系统 - 故障排除指南

## 🚀 快速启动

### 方法1：使用启动脚本
1. 双击 `启动系统.bat`
2. 等待系统检查完成
3. 浏览器会自动打开 http://localhost:3001

### 方法2：简单启动（如果启动脚本有问题）
1. 双击 `简单启动.bat`
2. 手动在浏览器中打开 http://localhost:3001

### 方法3：命令行启动
1. 打开命令提示符
2. 切换到项目目录
3. 运行 `node app.js`

## ❌ 常见问题解决

### 问题1：端口被占用
**现象**：提示端口3001已被占用
**解决方案**：
1. 关闭其他占用端口的程序
2. 或修改 `.env` 文件中的 `PORT=3001` 为其他端口

### 问题2：Node.js未安装
**现象**：提示"未找到Node.js"
**解决方案**：
1. 访问 https://nodejs.org/
2. 下载并安装最新版本的Node.js
3. 重启命令提示符

### 问题3：依赖安装失败
**现象**：npm install 失败
**解决方案**：
1. 检查网络连接
2. 尝试使用国内镜像：
   ```
   npm install --registry https://registry.npmmirror.com
   ```
3. 清除npm缓存：
   ```
   npm cache clean --force
   ```

### 问题4：数据库连接失败
**现象**：日志显示数据库连接错误
**解决方案**：
- 这是正常的！系统会自动使用文件存储模式
- 不影响系统正常使用

### 问题5：浏览器无法访问
**现象**：浏览器显示"无法访问此网站"
**解决方案**：
1. 确认服务器已启动（命令行窗口显示启动信息）
2. 检查防火墙设置
3. 尝试访问 http://127.0.0.1:3001

### 问题6：翻译功能不工作
**现象**：单词悬停没有翻译
**解决方案**：
1. 检查网络连接
2. 确认 `.env` 文件中的API密钥配置正确
3. 查看浏览器控制台是否有错误信息

## 🔧 高级故障排除

### 查看详细日志
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 查看Network标签页的网络请求状态

### 重置系统
1. 删除 `node_modules` 文件夹
2. 删除 `data` 文件夹（会清除所有数据）
3. 重新运行 `npm install`

### 检查系统状态
访问 http://localhost:3001/health 查看系统健康状态

## 📞 获取帮助

如果以上方法都无法解决问题：
1. 记录错误信息
2. 记录操作步骤
3. 提供系统环境信息（Windows版本、Node.js版本等）

## 🎯 系统要求

- **操作系统**：Windows 7/8/10/11
- **Node.js**：版本 14.0 或更高
- **内存**：至少 2GB RAM
- **磁盘空间**：至少 500MB 可用空间
- **网络**：需要互联网连接（用于翻译API）
