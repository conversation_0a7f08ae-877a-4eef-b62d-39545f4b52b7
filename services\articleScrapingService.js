/**
 * 文章抓取服务
 * 提供网页内容抓取、清理和结构化功能
 */

const axios = require('axios');
const cheerio = require('cheerio');
const { URL } = require('url');

class ArticleScrapingService {
  constructor() {
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    this.timeout = 30000; // 30秒超时
    this.maxRetries = 3;
  }

  /**
   * 抓取文章内容
   * @param {string} url - 文章URL
   * @param {Object} options - 抓取选项
   * @returns {Object} 抓取结果
   */
  async scrapeArticle(url, options = {}) {
    try {
      // 验证URL
      if (!this.isValidUrl(url)) {
        throw new Error('无效的URL格式');
      }

      // 获取网页内容
      const html = await this.fetchHtml(url, options);
      
      // 解析内容
      const articleData = this.parseArticle(html, url);
      
      return {
        success: true,
        url,
        timestamp: new Date().toISOString(),
        ...articleData
      };
    } catch (error) {
      return {
        success: false,
        url,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取网页HTML内容
   * @param {string} url - 目标URL
   * @param {Object} options - 请求选项
   * @returns {string} HTML内容
   */
  async fetchHtml(url, options = {}) {
    const config = {
      method: 'GET',
      url,
      timeout: options.timeout || this.timeout,
      headers: {
        'User-Agent': options.userAgent || this.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        ...options.headers
      },
      maxRedirects: 5,
      validateStatus: (status) => status < 400
    };

    let lastError;
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await axios(config);
        return response.data;
      } catch (error) {
        lastError = error;
        if (attempt < this.maxRetries) {
          // 等待后重试
          await this.delay(1000 * attempt);
        }
      }
    }

    throw new Error(`获取网页失败: ${lastError.message}`);
  }

  /**
   * 解析文章内容
   * @param {string} html - HTML内容
   * @param {string} url - 原始URL
   * @returns {Object} 解析结果
   */
  parseArticle(html, url) {
    const $ = cheerio.load(html);
    
    // 移除不需要的元素
    this.removeUnwantedElements($);
    
    // 提取基本信息
    const title = this.extractTitle($);
    const author = this.extractAuthor($);
    const publishDate = this.extractPublishDate($);
    const description = this.extractDescription($);
    
    // 提取正文内容
    const content = this.extractContent($);
    const plainText = this.extractPlainText(content);
    
    // 提取图片
    const images = this.extractImages($, url);
    
    // 提取链接
    const links = this.extractLinks($, url);
    
    // 提取标签/关键词
    const tags = this.extractTags($);
    
    return {
      title,
      author,
      publishDate,
      description,
      content,
      plainText,
      images,
      links,
      tags,
      wordCount: this.countWords(plainText),
      readingTime: this.estimateReadingTime(plainText)
    };
  }

  /**
   * 移除不需要的HTML元素
   * @param {Object} $ - Cheerio对象
   */
  removeUnwantedElements($) {
    // 移除脚本、样式、广告等
    const unwantedSelectors = [
      'script',
      'style',
      'noscript',
      'iframe',
      'embed',
      'object',
      '.advertisement',
      '.ads',
      '.ad',
      '.sidebar',
      '.navigation',
      '.nav',
      '.menu',
      '.header',
      '.footer',
      '.comment',
      '.comments',
      '.social',
      '.share',
      '.related',
      '.popup',
      '.modal',
      '[class*="ad"]',
      '[id*="ad"]',
      '[class*="advertisement"]',
      '[id*="advertisement"]'
    ];
    
    unwantedSelectors.forEach(selector => {
      $(selector).remove();
    });
  }

  /**
   * 提取标题
   * @param {Object} $ - Cheerio对象
   * @returns {string} 标题
   */
  extractTitle($) {
    const titleSelectors = [
      'h1',
      '.title',
      '.article-title',
      '.post-title',
      '.entry-title',
      '[class*="title"]',
      'title'
    ];
    
    for (const selector of titleSelectors) {
      const element = $(selector).first();
      if (element.length && element.text().trim()) {
        return element.text().trim();
      }
    }
    
    return $('title').text().trim() || '未找到标题';
  }

  /**
   * 提取作者
   * @param {Object} $ - Cheerio对象
   * @returns {string} 作者
   */
  extractAuthor($) {
    const authorSelectors = [
      '.author',
      '.byline',
      '.writer',
      '[class*="author"]',
      '[rel="author"]',
      '.post-author',
      '.article-author',
      'meta[name="author"]'
    ];
    
    for (const selector of authorSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const text = element.attr('content') || element.text().trim();
        if (text) return text;
      }
    }
    
    return '';
  }

  /**
   * 提取发布日期
   * @param {Object} $ - Cheerio对象
   * @returns {string} 发布日期
   */
  extractPublishDate($) {
    const dateSelectors = [
      'time[datetime]',
      '.date',
      '.publish-date',
      '.post-date',
      '.article-date',
      '[class*="date"]',
      'meta[property="article:published_time"]',
      'meta[name="date"]'
    ];
    
    for (const selector of dateSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const datetime = element.attr('datetime') || element.attr('content') || element.text().trim();
        if (datetime) {
          const date = new Date(datetime);
          if (!isNaN(date.getTime())) {
            return date.toISOString();
          }
        }
      }
    }
    
    return '';
  }

  /**
   * 提取描述
   * @param {Object} $ - Cheerio对象
   * @returns {string} 描述
   */
  extractDescription($) {
    const descriptionSelectors = [
      'meta[name="description"]',
      'meta[property="og:description"]',
      'meta[name="twitter:description"]',
      '.description',
      '.summary',
      '.excerpt',
      '.lead'
    ];
    
    for (const selector of descriptionSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const content = element.attr('content') || element.text().trim();
        if (content) return content;
      }
    }
    
    return '';
  }

  /**
   * 提取正文内容
   * @param {Object} $ - Cheerio对象
   * @returns {string} HTML内容
   */
  extractContent($) {
    const contentSelectors = [
      'article',
      '.content',
      '.article-content',
      '.post-content',
      '.entry-content',
      '.main-content',
      '[class*="content"]',
      '.text',
      '.body',
      'main'
    ];
    
    for (const selector of contentSelectors) {
      const element = $(selector).first();
      if (element.length && element.text().trim().length > 100) {
        return element.html();
      }
    }
    
    // 如果没找到合适的内容容器，尝试提取所有段落
    const paragraphs = $('p').map((i, el) => $(el).html()).get();
    if (paragraphs.length > 0) {
      return paragraphs.join('\n');
    }
    
    return $('body').html() || '';
  }

  /**
   * 提取纯文本内容
   * @param {string} htmlContent - HTML内容
   * @returns {string} 纯文本
   */
  extractPlainText(htmlContent) {
    if (!htmlContent) return '';
    
    const $ = cheerio.load(htmlContent);
    return $.text()
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * 提取图片
   * @param {Object} $ - Cheerio对象
   * @param {string} baseUrl - 基础URL
   * @returns {Array} 图片数组
   */
  extractImages($, baseUrl) {
    const images = [];
    
    $('img').each((i, el) => {
      const $img = $(el);
      const src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-lazy-src');
      
      if (src) {
        const absoluteUrl = this.resolveUrl(src, baseUrl);
        const alt = $img.attr('alt') || '';
        const title = $img.attr('title') || '';
        
        images.push({
          src: absoluteUrl,
          alt,
          title,
          width: $img.attr('width'),
          height: $img.attr('height')
        });
      }
    });
    
    return images;
  }

  /**
   * 提取链接
   * @param {Object} $ - Cheerio对象
   * @param {string} baseUrl - 基础URL
   * @returns {Array} 链接数组
   */
  extractLinks($, baseUrl) {
    const links = [];
    
    $('a[href]').each((i, el) => {
      const $link = $(el);
      const href = $link.attr('href');
      
      if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
        const absoluteUrl = this.resolveUrl(href, baseUrl);
        const text = $link.text().trim();
        const title = $link.attr('title') || '';
        
        if (text) {
          links.push({
            url: absoluteUrl,
            text,
            title
          });
        }
      }
    });
    
    return links;
  }

  /**
   * 提取标签
   * @param {Object} $ - Cheerio对象
   * @returns {Array} 标签数组
   */
  extractTags($) {
    const tags = new Set();
    
    // 从meta标签提取关键词
    const keywords = $('meta[name="keywords"]').attr('content');
    if (keywords) {
      keywords.split(',').forEach(tag => {
        const cleanTag = tag.trim();
        if (cleanTag) tags.add(cleanTag);
      });
    }
    
    // 从标签容器提取
    const tagSelectors = [
      '.tags a',
      '.tag',
      '.category',
      '.label',
      '[class*="tag"] a',
      '[class*="category"] a'
    ];
    
    tagSelectors.forEach(selector => {
      $(selector).each((i, el) => {
        const tag = $(el).text().trim();
        if (tag) tags.add(tag);
      });
    });
    
    return Array.from(tags);
  }

  /**
   * 统计单词数
   * @param {string} text - 文本内容
   * @returns {number} 单词数
   */
  countWords(text) {
    if (!text) return 0;
    
    // 英文单词
    const englishWords = (text.match(/\b[a-zA-Z]+\b/g) || []).length;
    
    // 中文字符
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    
    return englishWords + chineseChars;
  }

  /**
   * 估算阅读时间
   * @param {string} text - 文本内容
   * @returns {number} 阅读时间（分钟）
   */
  estimateReadingTime(text) {
    const wordCount = this.countWords(text);
    // 假设每分钟阅读200个单词/字符
    return Math.ceil(wordCount / 200);
  }

  /**
   * 解析相对URL为绝对URL
   * @param {string} url - 相对或绝对URL
   * @param {string} baseUrl - 基础URL
   * @returns {string} 绝对URL
   */
  resolveUrl(url, baseUrl) {
    try {
      return new URL(url, baseUrl).href;
    } catch {
      return url;
    }
  }

  /**
   * 验证URL格式
   * @param {string} url - URL字符串
   * @returns {boolean} 是否有效
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 批量抓取文章
   * @param {Array} urls - URL数组
   * @param {Object} options - 抓取选项
   * @returns {Array} 抓取结果数组
   */
  async scrapeMultipleArticles(urls, options = {}) {
    const results = [];
    const concurrency = options.concurrency || 3; // 并发数
    
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      const batchPromises = batch.map(url => this.scrapeArticle(url, options));
      
      try {
        const batchResults = await Promise.allSettled(batchPromises);
        batchResults.forEach(result => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            results.push({
              success: false,
              error: result.reason.message,
              timestamp: new Date().toISOString()
            });
          }
        });
      } catch (error) {
        console.error('批量抓取出错:', error);
      }
      
      // 批次间延迟，避免过于频繁的请求
      if (i + concurrency < urls.length) {
        await this.delay(options.batchDelay || 1000);
      }
    }
    
    return results;
  }
}

module.exports = ArticleScrapingService;